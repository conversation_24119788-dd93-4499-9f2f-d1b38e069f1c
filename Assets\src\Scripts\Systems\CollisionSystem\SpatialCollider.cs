using UnityEngine;
using Sirenix.OdinInspector;

public class SpatialCollider : MonoBehaviour, ICollidable, ISpawnable
{
        [Title("Collider Settings")]
        [EnumToggleButtons]
        [SerializeField] public ColliderShape shape = ColliderShape.Circle;        
        [ShowIf("shape", ColliderShape.Circle)]
        [SerializeField, Range(0.1f, 50f)] public float radius = 0.5f;        
        [ShowIf("shape", ColliderShape.Box)]
        [SerializeField] private Vector2 size = Vector2.one;
        
        [Title("Position")]
        [SerializeField] 
        [Tooltip("Offset from the GameObject's transform position")]
        private Vector2 colliderOffset = Vector2.zero;
        
        [SerializeField] private CollisionLayers layer = CollisionLayers.Player;
        [SerializeField] private bool isTrigger = false;
        
        [Title("Debug")]
        [SerializeField] private bool drawGizmos = true;
        [ShowIf("drawGizmos")]
        [SerializeField] private Color gizmoColor = Color.green;
        
        // ICollidable implementation
        public GameObject GameObject => gameObject;
        public Transform Transform => transform;
        public Vector2 Position => (Vector2)transform.position + colliderOffset;
        public CollisionLayers Layer => layer;
        public bool IsTrigger => isTrigger;
        public bool IsActive => gameObject.activeInHierarchy;
        public ColliderShape Shape => shape;
        public float Radius => radius;
        public Vector2 Size => size;
        public Vector2 Offset => colliderOffset;
        
        private Vector3 _lastPosition;
        private float _lastRadius;
        private bool _isRegistered = false;
        private ISpatialCollisionHandler[] _collisionHandlers;

        // Property setters for Collider2D synchronization
        public void SetRadius(float newRadius)
        {
            if (shape == ColliderShape.Circle && radius != newRadius)
            {
                radius = Mathf.Max(0.1f, newRadius);
                SynchronizeCollider2DProperties();
            }
        }

        public void SetSize(Vector2 newSize)
        {
            if (shape == ColliderShape.Box && size != newSize)
            {
                size = new Vector2(Mathf.Max(0.1f, newSize.x), Mathf.Max(0.1f, newSize.y));
                SynchronizeCollider2DProperties();
            }
        }

        public void SetOffset(Vector2 newOffset)
        {
            if (colliderOffset != newOffset)
            {
                colliderOffset = newOffset;
                SynchronizeCollider2DProperties();
            }
        }

        public void SetIsTrigger(bool newIsTrigger)
        {
            if (isTrigger != newIsTrigger)
            {
                isTrigger = newIsTrigger;
                SynchronizeCollider2DProperties();
            }
        }

        public void SetLayer(CollisionLayers newLayer)
        {
            if (layer != newLayer)
            {
                layer = newLayer;
                SynchronizeCollider2DProperties();
            }
        }

        public void SetShape(ColliderShape newShape)
        {
            if (shape != newShape)
            {
                shape = newShape;
                // Shape change requires recreating the Collider2D component
                EnsureCollider2DComponents();
            }
        }
        
        protected virtual void Awake()
        {
            // Cache all collision handlers on this GameObject
            _collisionHandlers = GetComponents<ISpatialCollisionHandler>();

            // Ensure Collider2D components are set up for Physics2D integration
            EnsureCollider2DComponents();
        }
        
        protected virtual void OnEnable()
        {
            _lastPosition = transform.position + (Vector3)colliderOffset;
            _lastRadius = radius;
            
            // Auto-register only for Player tagged objects
            // Pool objects use OnSpawn/OnDespawn for better performance
            if (gameObject.CompareTag("Player") && CollisionManager.Instance != null && !_isRegistered)
            {
                CollisionManager.Instance.RegisterCollidable(this);
                _isRegistered = true;
                
#if UNITY_EDITOR
                if (CollisionManager.Instance.LogCollisionEvents)
                {
                    Debug.Log($"SpatialCollider: Auto-registered Player {gameObject.name} on enable");
                }
#endif
            }
        }
        
        protected virtual void OnDisable()
        {
            // Auto-unregister only for Player tagged objects
            // Pool objects use OnSpawn/OnDespawn for better performance
            if (gameObject.CompareTag("Player") && CollisionManager.Instance != null && _isRegistered)
            {
                CollisionManager.Instance.UnregisterCollidable(this);
                _isRegistered = false;
                
#if UNITY_EDITOR
                if (CollisionManager.Instance.LogCollisionEvents)
                {
                    Debug.Log($"SpatialCollider: Auto-unregistered Player {gameObject.name} on disable");
                }
#endif
            }
        }
        
        protected virtual void OnDestroy()
        {
            // Safety cleanup - ensure we're unregistered if object is destroyed
            // This prevents memory leaks if OnDespawn() was not called properly
            if (_isRegistered && CollisionManager.Instance != null)
            {
                CollisionManager.Instance.UnregisterCollidable(this);
                _isRegistered = false;
            }
        }
        
        protected virtual void Update()
        {
            if (!_isRegistered || CollisionManager.Instance == null) return;
            
            Vector3 currentWorldPosition = transform.position + (Vector3)colliderOffset;
            bool positionChanged = Vector3.Distance(currentWorldPosition, _lastPosition) > 0.01f;
            bool radiusChanged = Mathf.Abs(radius - _lastRadius) > 0.01f;
            
            if (positionChanged || radiusChanged)
            {
                CollisionManager.Instance.UpdateCollidablePosition(this);

                // Synchronize Collider2D properties if using Physics2D mode
                SynchronizeCollider2DProperties();

                _lastPosition = currentWorldPosition;
                _lastRadius = radius;
            }
        }
        
        // Virtual methods for collision callbacks
        public virtual void OnSpatialCollisionEnter(CollisionInfo collision) 
        {
#if UNITY_EDITOR
            if (CollisionManager.Instance != null && CollisionManager.Instance.LogCollisionEvents)
            {
                Debug.Log($"CollisionEnter: {gameObject.name} <-> {collision.Other?.GameObject?.name}");
            }
#endif
            if (_collisionHandlers != null)
            {
                foreach (var handler in _collisionHandlers)
                {
                    handler.HandleCollisionEnter(collision);
                }
            }
        }
        
        public virtual void OnSpatialCollisionStay(CollisionInfo collision) 
        {
#if UNITY_EDITOR
            if (CollisionManager.Instance != null && CollisionManager.Instance.LogCollisionEvents)
            {
                Debug.Log($"CollisionStay: {gameObject.name} <-> {collision.Other?.GameObject?.name}");
            }
#endif
            if (_collisionHandlers != null)
            {
                foreach (var handler in _collisionHandlers)
                {
                    handler.HandleCollisionStay(collision);
                }
            }
        }
        
        public virtual void OnSpatialCollisionExit(CollisionInfo collision) 
        {
#if UNITY_EDITOR
            if (CollisionManager.Instance != null && CollisionManager.Instance.LogCollisionEvents)
            {
                Debug.Log($"CollisionExit: {gameObject.name} <-> {collision.Other?.GameObject?.name}");
            }
#endif
            if (_collisionHandlers != null)
            {
                foreach (var handler in _collisionHandlers)
                {
                    handler.HandleCollisionExit(collision);
                }
            }
        }
        
        public virtual void OnSpatialTriggerEnter(CollisionInfo collision) 
        {
#if UNITY_EDITOR
            if (CollisionManager.Instance != null && CollisionManager.Instance.LogTriggerEvents)
            {
                Debug.Log($"TriggerEnter: {gameObject.name} <-> {collision.Other?.GameObject?.name}");
            }
#endif
            if (_collisionHandlers != null)
            {
                foreach (var handler in _collisionHandlers)
                {
                    handler.HandleTriggerEnter(collision);
                }
            }
        }
        
        public virtual void OnSpatialTriggerStay(CollisionInfo collision) 
        {
#if UNITY_EDITOR
            if (CollisionManager.Instance != null && CollisionManager.Instance.LogTriggerEvents)
            {
                Debug.Log($"TriggerStay: {gameObject.name} <-> {collision.Other?.GameObject?.name}");
            }
#endif
            if (_collisionHandlers != null)
            {
                foreach (var handler in _collisionHandlers)
                {
                    handler.HandleTriggerStay(collision);
                }
            }
        }
        
        public virtual void OnSpatialTriggerExit(CollisionInfo collision) 
        {
#if UNITY_EDITOR
            if (CollisionManager.Instance != null && CollisionManager.Instance.LogTriggerEvents)
            {
                Debug.Log($"TriggerExit: {gameObject.name} <-> {collision.Other?.GameObject?.name}");
            }
#endif
            if (_collisionHandlers != null)
            {
                foreach (var handler in _collisionHandlers)
                {
                    handler.HandleTriggerExit(collision);
                }
            }
        }
        
        // Helper methods
        public void SetLayer(CollisionLayers newLayer)
        {
            layer = newLayer;
        }
        
        public void SetTrigger(bool trigger)
        {
            isTrigger = trigger;
        }
        
        public void SetOffset(Vector2 newOffset)
        {
            colliderOffset = newOffset;
            // Force position update if registered
            if (_isRegistered && CollisionManager.Instance != null)
            {
                CollisionManager.Instance.UpdateCollidablePosition(this);
                _lastPosition = transform.position + (Vector3)colliderOffset;
            }
        }
        
        /// <summary>
        /// Forces an immediate collision update - useful when radius changes rapidly during animations
        /// </summary>
        public void ForceCollisionUpdate()
        {
            if (_isRegistered && CollisionManager.Instance != null)
            {
                CollisionManager.Instance.UpdateCollidablePosition(this);
                _lastPosition = transform.position + (Vector3)colliderOffset;
                _lastRadius = radius;
            }
        }
        
        /// <summary>
        /// Manual registration for non-pooled objects
        /// Call this if the object is not managed by the pool system
        /// </summary>
        public void ManualRegister()
        {
            if (CollisionManager.Instance != null && !_isRegistered)
            {
                CollisionManager.Instance.RegisterCollidable(this);
                _isRegistered = true;
                _lastPosition = transform.position + (Vector3)colliderOffset;
                _lastRadius = radius;
                
#if UNITY_EDITOR
                if (CollisionManager.Instance.LogCollisionEvents)
                {
                    Debug.Log($"SpatialCollider: Manually registered {gameObject.name}");
                }
#endif
            }
        }
        
        /// <summary>
        /// Manual unregistration for non-pooled objects
        /// Call this before destroying non-pooled objects
        /// </summary>
        public void ManualUnregister()
        {
            if (CollisionManager.Instance != null && _isRegistered)
            {
                CollisionManager.Instance.UnregisterCollidable(this);
                _isRegistered = false;
                
#if UNITY_EDITOR
                if (CollisionManager.Instance.LogCollisionEvents)
                {
                    Debug.Log($"SpatialCollider: Manually unregistered {gameObject.name}");
                }
#endif
            }
        }
        
        /// <summary>
        /// Check if this collider is currently registered with the collision system
        /// </summary>
        public bool IsRegistered => _isRegistered;
        
        // Debug visualization
        private void OnDrawGizmos()
        {
            if (!drawGizmos) return;
            
            Gizmos.color = gizmoColor;
            Vector3 offsetPosition = transform.position + (Vector3)colliderOffset;
            
            if (shape == ColliderShape.Circle)
            {
                DrawCircleGizmo(offsetPosition, radius);
            }
            else
            {
                DrawBoxGizmo(offsetPosition, size);
            }
            
            // Draw line from transform to offset position if offset is not zero
            if (colliderOffset != Vector2.zero)
            {
                Gizmos.color = gizmoColor * 0.5f;
                Gizmos.DrawLine(transform.position, offsetPosition);
                
                // Draw small cross at transform position
                float crossSize = 0.1f;
                Gizmos.DrawLine(transform.position - Vector3.right * crossSize, transform.position + Vector3.right * crossSize);
                Gizmos.DrawLine(transform.position - Vector3.up * crossSize, transform.position + Vector3.up * crossSize);
            }
        }
        
        private void DrawCircleGizmo(Vector3 center, float r)
        {
            int segments = 32;
            float angleStep = 360f / segments;
            
            for (int i = 0; i < segments; i++)
            {
                float angle1 = i * angleStep * Mathf.Deg2Rad;
                float angle2 = (i + 1) * angleStep * Mathf.Deg2Rad;
                
                Vector3 point1 = center + new Vector3(Mathf.Cos(angle1) * r, Mathf.Sin(angle1) * r, 0);
                Vector3 point2 = center + new Vector3(Mathf.Cos(angle2) * r, Mathf.Sin(angle2) * r, 0);
                
                Gizmos.DrawLine(point1, point2);
            }
        }
        
        private void DrawBoxGizmo(Vector3 center, Vector2 s)
        {
            Vector3 halfSize = new Vector3(s.x * 0.5f, s.y * 0.5f, 0);
            
            Vector3 topLeft = center + new Vector3(-halfSize.x, halfSize.y, 0);
            Vector3 topRight = center + new Vector3(halfSize.x, halfSize.y, 0);
            Vector3 bottomLeft = center + new Vector3(-halfSize.x, -halfSize.y, 0);
            Vector3 bottomRight = center + new Vector3(halfSize.x, -halfSize.y, 0);
            
            Gizmos.DrawLine(topLeft, topRight);
            Gizmos.DrawLine(topRight, bottomRight);
            Gizmos.DrawLine(bottomRight, bottomLeft);
            Gizmos.DrawLine(bottomLeft, topLeft);
        }

    public void OnSpawn()
    {
        // CRITICAL FIX: Always register on spawn to ensure correct chunk tracking
        // This prevents race conditions where objects spawning in different chunks lose collision detection
        if (CollisionManager.Instance != null)
        {
            // Only register if not already registered (double-registration protection)
            if (!_isRegistered)
            {
                CollisionManager.Instance.RegisterCollidable(this);
                _isRegistered = true;
                
#if UNITY_EDITOR
                if (CollisionManager.Instance.LogCollisionEvents)
                {
                    Debug.Log($"SpatialCollider: Registered {gameObject.name} on spawn (pooled object)");
                }
#endif
            }
            else
            {
                // Object was previously registered but despawned - update position tracking
                // This ensures chunk-to-object mapping is correct for new spawn location
                CollisionManager.Instance.UpdateCollidablePosition(this);
                
#if UNITY_EDITOR
                if (CollisionManager.Instance.LogCollisionEvents)
                {
                    Debug.Log($"SpatialCollider: Updated position for {gameObject.name} on respawn");
                }
#endif
            }
        }
        
        // Reset position tracking for active state
        _lastPosition = transform.position + (Vector3)colliderOffset;
        _lastRadius = radius;
    }

    public void OnDespawn()
    {
        // CRITICAL FIX: Unregister on despawn to prevent stale chunk tracking
        // This ensures clean state and prevents chunk-to-object mapping corruption
        if (CollisionManager.Instance != null && _isRegistered)
        {
            CollisionManager.Instance.UnregisterCollidable(this);
            _isRegistered = false;
            
#if UNITY_EDITOR
            if (CollisionManager.Instance.LogCollisionEvents)
            {
                Debug.Log($"SpatialCollider: Unregistered {gameObject.name} on despawn (clean pooled object handling)");
            }
#endif
        }

        /// <summary>
        /// Ensure appropriate Collider2D components are present for Physics2D integration
        /// </summary>
        private void EnsureCollider2DComponents()
        {
            // Only set up Collider2D if CollisionManager is using Physics2D mode
            if (CollisionManager.Instance != null &&
                CollisionManager.Instance.GetCollisionMode() == CollisionDetectionMode.Physics2D)
            {
                var layerMapping = CollisionManager.Instance.GetPhysics2DLayerMapping();
                if (layerMapping != null)
                {
                    Collider2DAutoManager.EnsureCollider2D(this, layerMapping);
                }
            }
        }

        /// <summary>
        /// Synchronize Collider2D properties with SpatialCollider settings
        /// </summary>
        private void SynchronizeCollider2DProperties()
        {
            // Only synchronize if CollisionManager is using Physics2D mode
            if (CollisionManager.Instance != null &&
                CollisionManager.Instance.GetCollisionMode() == CollisionDetectionMode.Physics2D)
            {
                var layerMapping = CollisionManager.Instance.GetPhysics2DLayerMapping();
                if (layerMapping != null)
                {
                    Collider2DAutoManager.SynchronizeProperties(this, layerMapping);
                }
            }
        }
    }
}
using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Shock status effect that can chain lightning damage to nearby enemies.
/// Typically applied by Lightning damage type attacks.
/// </summary>
public class ShockEffect : StatusEffect
{
    private float chainDamage;
    private float chainRange;
    private IDamageable targetHealth;
    
    public ShockEffect(float chainDamage, float chainRange = 3f, float duration = 2f, string sourceId = "") 
        : base(StatusEffectType.Shock, duration, 0.5f, sourceId)
    {
        this.chainDamage = chainDamage;
        this.chainRange = chainRange;
    }
    
    protected override void OnApply()
    {
        // Cache the health component - use appropriate method based on object type
        if (target != null)
        {
            // Check if target is player and use PlayerManager
            if (target.CompareTag("Player"))
            {
                var playerHealth = PlayerManager.PlayerHealth;
                var legacyHealth = PlayerManager.LegacyHealthComponent;
                
                if (playerHealth != null)
                {
                    targetHealth = playerHealth;
                }
                else if (legacyHealth != null)
                {
                    targetHealth = legacyHealth;
                }
                else
                {
                    targetHealth = null;
                }
            }
            else
            {
                // For non-player objects, try PoolManager first, then fallback to GetComponent
                if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
                {
                    targetHealth = combatantHealth;
                }
                else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
                {
                    targetHealth = healthComponent;
                }
                else
                {
                    targetHealth = null;
                }
            }
        }
    }
    
    protected override void OnTick()
    {
        if (target != null && target.activeInHierarchy)
        {
            // Chain lightning to nearby enemies
            ChainToNearbyEnemies();
        }
    }
    
    protected override void OnRemove()
    {
        // Cleanup if needed
        targetHealth = null;
    }
    
    private void ChainToNearbyEnemies()
    {
        if (target == null || CollisionManager.Instance == null) return;
        
        // Use custom collision system instead of Unity Physics
        List<ICollidable> nearbyObjects = new List<ICollidable>();
        CollisionManager.Instance.GetCollidersInRadiusNonAlloc(
            target.transform.position, 
            chainRange, 
            nearbyObjects, 
            CollisionLayers.Enemy | CollisionLayers.Player
        );
        
        List<GameObject> validTargets = new List<GameObject>();
        
        foreach (var collidable in nearbyObjects)
        {
            // Skip the original target
            if (collidable.GameObject == target) continue;
            
            // Skip inactive objects
            if (!collidable.IsActive) continue;
            
            // Check if the target has a health component
            bool hasHealthComponent = false;
            GameObject targetObject = collidable.GameObject;
            
            // Check if target is player and use PlayerManager
            if (targetObject.CompareTag("Player"))
            {
                hasHealthComponent = PlayerManager.PlayerHealth != null || PlayerManager.LegacyHealthComponent != null;
            }
            else
            {
                // For non-player objects, try PoolManager first
                if (PoolManager.Instance != null && 
                    (PoolManager.Instance.GetCachedComponent<CombatantHealth>(targetObject, out var _) ||
                     PoolManager.Instance.GetCachedComponent<HealthComponent>(targetObject, out var _)))
                {
                    hasHealthComponent = true;
                }
                else
                {
                    hasHealthComponent = false;
                }
            }
            
            if (hasHealthComponent)
            {
                if (!targetObject.CompareTag("Player"))
                {
                    validTargets.Add(targetObject);
                }
            }
        }
        
        // Chain to up to 2 nearby enemies per tick
        int chainCount = Mathf.Min(2, validTargets.Count);
        for (int i = 0; i < chainCount; i++)
        {
            GameObject chainTarget = validTargets[i];
            IDamageable chainTargetHealth = null;
            
            // Get health component using appropriate method
            if (chainTarget.CompareTag("Player"))
            {
                // Use PlayerManager for player components
                chainTargetHealth = PlayerManager.PlayerHealth as IDamageable ?? PlayerManager.LegacyHealthComponent as IDamageable;
            }
            else
            {
                // For non-player objects, try PoolManager first, then fallback to GetComponent
                if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(chainTarget, out var combatantHealth))
                {
                    chainTargetHealth = combatantHealth;
                }
                else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(chainTarget, out var healthComponent))
                {
                    chainTargetHealth = healthComponent;
                }
                else
                {
                    // No fallback - non-pooled objects should use PlayerManager
                    chainTargetHealth = null;
                }
            }
            
            if (chainTargetHealth != null)
            {
                // Create chain damage info with breakdown
                DamageInfo chainDamageInfo = DamageInfo.FromSingleType(
                    chainDamage,
                    DamageType.Lightning,
                    false,
                    1f,
                    $"Shock_Chain_{SourceId}"
                );
                
                chainTargetHealth.TakeDamage(chainDamageInfo);
                
                // Visual effect could be added here
               
            }
        }
    }
}

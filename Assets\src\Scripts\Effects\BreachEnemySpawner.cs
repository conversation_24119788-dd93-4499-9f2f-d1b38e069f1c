using UnityEngine;
using Sirenix.OdinInspector;
using MEC;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// Handles enemy spawning for breach effects.
/// Separated from BreachEffect to follow Single Responsibility Principle.
/// Uses MEC coroutines and Queue for delayed spawning to eliminate performance overhead.
/// </summary>
[RequireComponent(typeof(SpatialCollider))]
public class BreachEnemySpawner : MonoBehaviour, ISpawnable
{
    [Title("Enemy Spawning Configuration")]
    [InfoBox("Spawns enemies within the breach radius using race-based selection and particle effects. Enable 'Use Custom Values' to override specific parameters.", InfoMessageType.Info)]
    
    [SerializeField]
    [Tooltip("Settings asset containing default values for all breach effects")]
    private BreachEffectSettings settings;
    
    [SerializeField]
    [Tooltip("Enable enemy spawning within breach radius")]
    private bool enableSpawning = false;
    
    [ShowIf("enableSpawning")]
    [SerializeField]
    [Tooltip("Use custom values instead of settings defaults")]
    private bool useCustomValues = false;
    
    [ShowIf("enableSpawning")]
    [SerializeField]
    [Required]
    [Tooltip("Enemy race data to spawn from")]
    private EnemyRaceData enemyRaceData;
    
    [Title("Custom Overrides")]
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField, MinValue(0.1f)]
    [Tooltip("Interval between enemy spawns in seconds (overrides settings)")]
    private float customSpawnInterval = 2f;
    
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField, MinValue(1)]
    [Tooltip("Minimum number of enemies to spawn per interval (overrides settings)")]
    private int customMinEnemiesPerSpawn = 1;
    
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField, MinValue(1)]
    [Tooltip("Maximum number of enemies to spawn per interval (overrides settings)")]
    private int customMaxEnemiesPerSpawn = 3;
    
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField, MinValue(1)]
    [Tooltip("Maximum total number of enemies that can be spawned (overrides settings)")]
    private int customMaxSpawnedEnemies = 5;
    
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField, Range(0.1f, 0.9f)]
    [Tooltip("Minimum radius factor for spawning (overrides settings)")]
    private float customMinSpawnRadiusFactor = 0.3f;
    
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField, Range(0.6f, 1f)]
    [Tooltip("Maximum radius factor for spawning (overrides settings)")]
    private float customMaxSpawnRadiusFactor = 0.8f;
    
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField, MinValue(0f)]
    [Tooltip("Delay before starting enemy spawning (overrides settings)")]
    private float customSpawnDelay = 0.5f;
    
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField]
    [Tooltip("Particle effect to spawn before each enemy appears (overrides settings)")]
    private ParticleType customSpawnParticleType = ParticleType.BreachSpawnEffect;
    
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField, MinValue(0f)]
    [Tooltip("Delay between particle effect and enemy spawn (overrides settings)")]
    private float customParticleToEnemyDelay = 0.3f;
    
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField]
    [Tooltip("Enemy type to assign to spawned enemies (overrides settings)")]
    private EnemyType customBreachEnemyType = EnemyType.Fire;
    
    [ShowIf("@enableSpawning && useCustomValues")]
    [SerializeField]
    [Tooltip("Splinter types that can drop from this breach (overrides settings)")]
    private SplinterType customAllowedSplinterTypes = SplinterType.All;
    
    [Title("Splinter Drop Settings")]
    [ShowIf("enableSpawning")]
    [SerializeField]
    [Tooltip("Enable splinter drops for breach-spawned enemies")]
    private bool enableSplinterDrops = true;
    
    [ShowIf("@enableSpawning && enableSplinterDrops")]
    [SerializeField]
    [Required]
    [Tooltip("Splinter drop configuration for this breach type")]
    private SplinterDropConfig breachSplinterConfig;
    
    [SerializeField]
    [Tooltip("Enable debug logging")]
    private bool enableDebugLogging = false;
    
    // Runtime references
    private SpatialCollider spatialCollider;
    private CoroutineHandle spawnCoroutine;
    private CoroutineHandle delayedSpawnCoroutine;
    
    // Optimized delayed spawning - Queue for efficient processing
    private Queue<DelayedSpawn> pendingSpawns = new Queue<DelayedSpawn>();
    
    // Performance caching
    private List<ICollidable> cachedCollisionList = new List<ICollidable>();
    
    // Runtime state
    private bool isSpawning = false;
    private int enemiesSpawned = 0;
    private int chunkLevel = 1;
    
    // Chunk tracking
    private ChunkCoordinate currentChunk;
    private ChunkContentSpawner chunkSpawner;
    private static ChunkContentSpawner cachedChunkSpawner;
    
    // Coordination flags
    private bool isCleaningUp = false;
    
    private struct DelayedSpawn
    {
        public WeightedEnemyType enemyType;
        public Vector3 position;
        public float spawnTime;
    }
    
    // Settings-aware properties that return either custom values or settings defaults
    public float SpawnInterval => useCustomValues ? customSpawnInterval : (settings?.defaultSpawnInterval ?? 2f);
    public int MinEnemiesPerSpawn => useCustomValues ? customMinEnemiesPerSpawn : (settings?.defaultMinEnemiesPerSpawn ?? 1);
    public int MaxEnemiesPerSpawn => useCustomValues ? customMaxEnemiesPerSpawn : (settings?.defaultMaxEnemiesPerSpawn ?? 3);
    public int MaxSpawnedEnemies => useCustomValues ? customMaxSpawnedEnemies : (settings?.defaultMaxSpawnedEnemies ?? 5);
    public float MinSpawnRadiusFactor => useCustomValues ? customMinSpawnRadiusFactor : (settings?.defaultMinSpawnRadiusFactor ?? 0.3f);
    public float MaxSpawnRadiusFactor => useCustomValues ? customMaxSpawnRadiusFactor : (settings?.defaultMaxSpawnRadiusFactor ?? 0.8f);
    public float SpawnDelay => useCustomValues ? customSpawnDelay : (settings?.defaultEnemySpawnDelay ?? 0.5f);
    public ParticleType SpawnParticleType => useCustomValues ? customSpawnParticleType : (settings?.defaultSpawnParticleType ?? ParticleType.BreachSpawnEffect);
    public float ParticleToEnemyDelay => useCustomValues ? customParticleToEnemyDelay : (settings?.defaultParticleToEnemyDelay ?? 0.3f);
    public EnemyType BreachEnemyType => useCustomValues ? customBreachEnemyType : (settings?.defaultBreachEnemyType ?? EnemyType.Fire);
    public SplinterType AllowedSplinterTypes => useCustomValues ? customAllowedSplinterTypes : (settings?.defaultAllowedSplinterTypes ?? SplinterType.All);
    
    // Properties for external access
    public bool IsSpawning => isSpawning;
    public int EnemiesSpawned => enemiesSpawned;
    public bool EnableSpawning 
    { 
        get => enableSpawning; 
        set => enableSpawning = value; 
    }
    
    #region Unity Lifecycle
    void Awake()
    {
        spatialCollider = GetComponent<SpatialCollider>();
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Initialized");
        }
    }
    
    void OnDisable()
    {
        StopSpawning();
        // Removed CleanupSpawnedEnemies() - handled by BreachEnemyManager
    }
    
    void OnDestroy()
    {
        StopAllMECCoroutines();
        // Removed CleanupSpawnedEnemies() - handled by BreachEnemyManager
    }
    #endregion
    
    #region Public Methods
    /// <summary>
    /// Starts enemy spawning with configured delay
    /// </summary>
    [Button("Start Spawning", ButtonSizes.Large)]
    public void StartSpawning()
    {
        if (!enableSpawning || enemyRaceData == null)
        {
            if (enableDebugLogging)
            {
                Debug.LogWarning($"[BreachEnemySpawner] {gameObject.name}: Cannot start spawning - disabled or no race data");
            }
            return;
        }
        
        if (isSpawning)
        {
            if (enableDebugLogging)
            {
                Debug.LogWarning($"[BreachEnemySpawner] {gameObject.name}: Already spawning");
            }
            return;
        }
        
        if (!enemyRaceData.HasEnemiesAtLevel(chunkLevel))
        {
            if (enableDebugLogging)
            {
                Debug.LogWarning($"[BreachEnemySpawner] {gameObject.name}: No enemies available at level {chunkLevel}");
            }
            return;
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Starting spawning with {SpawnDelay}s delay");
        }
        
        // Start spawning with delay
        if (SpawnDelay > 0f)
        {
            spawnCoroutine = Timing.RunCoroutine(StartSpawningAfterDelay(), Segment.Update);
        }
        else
        {
            BeginIntervalSpawning();
        }
    }
    
    /// <summary>
    /// Stops enemy spawning and clears queued spawns
    /// </summary>
    [Button("Stop Spawning", ButtonSizes.Medium)]
    public void StopSpawning()
    {
        StopAllMECCoroutines();
        pendingSpawns.Clear();
        isSpawning = false;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Stopped spawning");
        }
    }
    
    // Removed CleanupSpawnedEnemies() method - enemy cleanup now handled by BreachEnemyManager
    // This eliminates dual-ownership race conditions and cleanup order dependencies
    
    /// <summary>
    /// Sets the chunk level for enemy scaling
    /// </summary>
    public void SetChunkLevel(int level)
    {
        chunkLevel = Mathf.Max(1, level);
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Set chunk level to {chunkLevel}");
        }
    }
    #endregion
    
    #region Private Spawning Logic
    private void BeginIntervalSpawning()
    {
        if (!enableSpawning || enemyRaceData == null || !enemyRaceData.HasEnemiesAtLevel(chunkLevel))
            return;
            
        isSpawning = true;
        enemiesSpawned = 0;
        
        // Setup chunk tracking for enemy registration
        SetupChunkTracking();
        
        // Start interval-based spawning
        spawnCoroutine = Timing.RunCoroutine(IntervalSpawningCoroutine(), Segment.Update);
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Started interval spawning - Max: {MaxSpawnedEnemies}, Per Batch: {MinEnemiesPerSpawn}-{MaxEnemiesPerSpawn}");
        }
    }
    
    private IEnumerator<float> StartSpawningAfterDelay()
    {
        yield return Timing.WaitForSeconds(SpawnDelay);
        BeginIntervalSpawning();
    }
    
    private IEnumerator<float> IntervalSpawningCoroutine()
    {
        while (isSpawning && enemiesSpawned < MaxSpawnedEnemies)
        {
            // Spawn batch of enemies
            SpawnEnemyBatch();
            
            if (enableDebugLogging && enemiesSpawned >= MaxSpawnedEnemies)
            {
                Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Reached maximum enemies ({MaxSpawnedEnemies})");
                break;
            }
            
            // Wait for next interval
            if (enemiesSpawned < MaxSpawnedEnemies)
            {
                yield return Timing.WaitForSeconds(SpawnInterval);
            }
        }
    }
    
    private void SpawnEnemyBatch()
    {
        if (!isSpawning || spatialCollider == null || PoolManager.Instance == null)
            return;
            
        var availableEnemies = enemyRaceData.GetAvailableEnemyTypes(chunkLevel);
        if (availableEnemies.Length == 0) return;
        
        // Calculate batch size
        int remainingSlots = MaxSpawnedEnemies - enemiesSpawned;
        int desiredEnemies = Random.Range(MinEnemiesPerSpawn, MaxEnemiesPerSpawn + 1);
        int enemiesToSpawn = Mathf.Min(desiredEnemies, remainingSlots);
        
        if (enemiesToSpawn <= 0) return;
        
        float totalWeight = enemyRaceData.GetTotalWeight(chunkLevel);
        
        // Spawn each enemy with particle effects and delays
        for (int i = 0; i < enemiesToSpawn; i++)
        {
            // Select enemy type using weighted selection
            WeightedEnemyType selectedEnemy = SelectEnemyType(availableEnemies, totalWeight);
            if (selectedEnemy?.Prefab == null) continue;
            
            // Find valid spawn position
            Vector3 spawnPosition = FindValidSpawnPosition();
            if (spawnPosition == Vector3.zero) continue;
            
            // Spawn particle effect immediately
            SpawnParticleEffect(spawnPosition);
            
            // Schedule enemy spawn after particle delay
            if (ParticleToEnemyDelay > 0f)
            {
                ScheduleDelayedSpawn(selectedEnemy, spawnPosition);
            }
            else
            {
                SpawnEnemyAtPosition(selectedEnemy, spawnPosition);
            }
        }
    }
    
    private WeightedEnemyType SelectEnemyType(WeightedEnemyType[] availableEnemies, float totalWeight)
    {
        float randomValue = Random.Range(0f, totalWeight);
        float currentWeight = 0f;
        
        foreach (var enemy in availableEnemies)
        {
            currentWeight += enemy.Weight;
            if (randomValue <= currentWeight)
            {
                return enemy;
            }
        }
        
        return availableEnemies[availableEnemies.Length - 1]; // Fallback
    }
    
    private void SpawnParticleEffect(Vector3 position)
    {
        if (ParticleEffectManager.Instance != null && HasValidParticleSystem(SpawnParticleType))
        {
            ParticleEffectManager.Instance.SpawnParticle(SpawnParticleType, position, 1);
            
            if (enableDebugLogging)
            {
                Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Spawned particle {SpawnParticleType} at {position}");
            }
        }
    }
    
    private void ScheduleDelayedSpawn(WeightedEnemyType enemyType, Vector3 position)
    {
        pendingSpawns.Enqueue(new DelayedSpawn
        {
            enemyType = enemyType,
            position = position,
            spawnTime = Time.time + ParticleToEnemyDelay
        });
        
        // Always start processing delayed spawns - kill previous if still running
        if (delayedSpawnCoroutine.IsValid)
        {
            Timing.KillCoroutines(delayedSpawnCoroutine);
        }
        delayedSpawnCoroutine = Timing.RunCoroutine(DelayedSpawnProcessor(), Segment.Update);
    }
    
    private IEnumerator<float> DelayedSpawnProcessor()
    {
        while (pendingSpawns.Count > 0)
        {
            float currentTime = Time.time;
            int processedCount = 0;
            int originalCount = pendingSpawns.Count;
            
            // Process all spawns that are ready
            for (int i = 0; i < originalCount; i++)
            {
                if (pendingSpawns.Count == 0) break;
                
                DelayedSpawn spawn = pendingSpawns.Peek();
                if (currentTime >= spawn.spawnTime)
                {
                    pendingSpawns.Dequeue();
                    SpawnEnemyAtPosition(spawn.enemyType, spawn.position);
                    processedCount++;
                }
                else
                {
                    break; // No more ready spawns this frame
                }
            }
            
            // Short wait before checking again
            yield return Timing.WaitForSeconds(0.1f);
        }
    }
    
    private void SpawnEnemyAtPosition(WeightedEnemyType enemyType, Vector3 position)
    {
        if (!isSpawning || enemyType?.Prefab == null || PoolManager.Instance == null)
            return;
            
        if (enemiesSpawned >= MaxSpawnedEnemies) return;
        
        GameObject spawnedEnemy = PoolManager.Instance.Spawn(enemyType.Prefab, position, Quaternion.identity);
        if (spawnedEnemy != null)
        {
            enemiesSpawned++;
            
            // Register with BreachEnemyManager instead of local tracking
            if (BreachEnemyManager.Instance != null)
            {
                BreachEnemyManager.Instance.RegisterBreachEnemy(spawnedEnemy, currentChunk);
            }
            
            // Setup enemy components
            SetupSpawnedEnemy(spawnedEnemy);
            
            if (enableDebugLogging)
            {
                Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Spawned {enemyType.Name} at {position} ({enemiesSpawned}/{MaxSpawnedEnemies}) - registered with BreachEnemyManager");
            }
        }
    }
    
    private void SetupSpawnedEnemy(GameObject enemy)
    {
        // Add breach marker for splinter drops
        if (enableSplinterDrops && breachSplinterConfig != null)
        {
            var marker = enemy.GetComponent<BreachSpawnedMarker>();
            if (marker == null)
                marker = enemy.AddComponent<BreachSpawnedMarker>();
            
            marker.Initialize(gameObject.name, breachSplinterConfig, AllowedSplinterTypes);
        }
        
        // Set enemy type
        if (PoolManager.Instance.GetCachedComponent<BaseEnemy>(enemy, out BaseEnemy baseEnemy))
        {
            baseEnemy.SetEnemyType(BreachEnemyType);
        }
        
        // BreachEnemySpawner maintains sole ownership of its spawned enemies
        // Removed RegisterEnemyWithChunkSpawner to eliminate race conditions
        
        // Set chunk bounds for pathfinding
        if (PoolManager.Instance.GetCachedComponent<PathfindingMovement>(enemy, out PathfindingMovement pathfindingMovement))
        {
            if (TilemapChunkManager.Instance != null)
            {
                Bounds chunkBounds = TilemapChunkManager.Instance.GetChunkBounds(currentChunk);
                pathfindingMovement.SetChunkBounds(chunkBounds);
            }
        }
    }
    
    private Vector3 FindValidSpawnPosition()
    {
        if (spatialCollider == null) return Vector3.zero;
        
        float currentRadius = spatialCollider.radius;
        float minRadius = currentRadius * MinSpawnRadiusFactor;
        float maxRadius = currentRadius * MaxSpawnRadiusFactor;
        
        // Try multiple positions to find a valid one
        for (int attempts = 0; attempts < 10; attempts++)
        {
            float angle = Random.Range(0f, 360f) * Mathf.Deg2Rad;
            float distance = Random.Range(minRadius, maxRadius);
            
            Vector3 offset = new Vector3(
                Mathf.Cos(angle) * distance,
                Mathf.Sin(angle) * distance,
                0f
            );
            
            Vector3 candidatePosition = transform.position + offset;
            
            if (IsValidSpawnPosition(candidatePosition))
            {
                return candidatePosition;
            }
        }
        
        return Vector3.zero;
    }
    
    private bool IsValidSpawnPosition(Vector3 position)
    {
        if (CollisionManager.Instance != null)
        {
            cachedCollisionList.Clear();
            CollisionManager.Instance.GetCollidersInRadiusNonAlloc(position, 1f, cachedCollisionList);
            
            foreach (var collidable in cachedCollisionList)
            {
                if (collidable.Layer.HasFlag(CollisionLayers.Enemy) || 
                    collidable.Layer.HasFlag(CollisionLayers.Player) ||
                    collidable.Layer.HasFlag(CollisionLayers.Wall))
                {
                    float distance = Vector3.Distance(position, collidable.Position);
                    if (distance < 2f) return false;
                }
            }
        }
        
        return true;
    }
    
    private bool HasValidParticleSystem(ParticleType particleType)
    {
        if (ParticleEffectManager.Instance == null) return false;
        
        switch (particleType)
        {
            case ParticleType.BreachSpawnEffect:
                return ParticleEffectManager.Instance.breachSpawnEffect != null;
            case ParticleType.BloodImpact:
                return ParticleEffectManager.Instance.bloodImpactVariants != null && 
                       ParticleEffectManager.Instance.bloodImpactVariants.Length > 0;
            default:
                return false;
        }
    }
    #endregion
    
    #region Chunk Tracking
    private void SetupChunkTracking()
    {
        if (TilemapChunkManager.Instance != null)
        {
            currentChunk = ChunkCoordinate.FromWorldPosition(
                transform.position,
                TilemapChunkManager.Instance.GetChunkWidth(),
                TilemapChunkManager.Instance.GetChunkHeight()
            );
        }
        
        if (cachedChunkSpawner == null)
        {
            cachedChunkSpawner = FindFirstObjectByType<ChunkContentSpawner>();
        }
        chunkSpawner = cachedChunkSpawner;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Setup chunk tracking - chunk: {currentChunk}");
        }
    }
    
    // Removed RegisterEnemyWithChunkSpawner and UnregisterEnemyFromChunkSpawner methods
    // BreachEnemySpawner now maintains sole ownership of its spawned enemies
    // This eliminates race conditions with ChunkContentSpawner dual tracking
    
    private void StopAllMECCoroutines()
    {
        if (spawnCoroutine.IsValid)
        {
            Timing.KillCoroutines(spawnCoroutine);
        }
        if (delayedSpawnCoroutine.IsValid)
        {
            Timing.KillCoroutines(delayedSpawnCoroutine);
        }
    }
    #endregion
    
    #region ISpawnable Implementation
    public void OnSpawn()
    {
        // Reset state for pooled object
        isSpawning = false;
        enemiesSpawned = 0;
        isCleaningUp = false;
        pendingSpawns.Clear();
        
        // Auto-detect chunk level when spawned
        chunkLevel = CalculateCurrentChunkLevel();
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Spawned from pool - level: {chunkLevel}");
        }
    }
    
    public void OnDespawn()
    {
        StopSpawning();
        
        // Use BreachEnemyManager for centralized cleanup
        if (BreachEnemyManager.Instance != null)
        {
            BreachEnemyManager.Instance.DespawnAllEnemiesFromSpawner(this);
        }
        
        // Reset state for pooled object
        isSpawning = false;
        enemiesSpawned = 0;
        isCleaningUp = false;
        pendingSpawns.Clear();
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachEnemySpawner] {gameObject.name}: Returning to pool - enemies handled by BreachEnemyManager");
        }
    }
    
    private int CalculateCurrentChunkLevel()
    {
        if (TilemapChunkManager.Instance == null) return 1;
        
        ChunkCoordinate breachChunk = ChunkCoordinate.FromWorldPosition(
            transform.position, 
            TilemapChunkManager.Instance.GetChunkWidth(), 
            TilemapChunkManager.Instance.GetChunkHeight()
        );
        
        int distanceFromSpawn = Mathf.Max(Mathf.Abs(breachChunk.x), Mathf.Abs(breachChunk.y));
        return Mathf.Max(1, distanceFromSpawn);
    }
    #endregion
    
    #region Debug & Inspector
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    [ShowIf("@UnityEngine.Application.isPlaying")]
    private string spawningStatus => $"Spawning: {isSpawning} | Enemies: {enemiesSpawned}/{MaxSpawnedEnemies} | Pending: {pendingSpawns.Count} | Level: {chunkLevel} | Source: {(useCustomValues ? "Custom" : "Settings")} | Manager: {(BreachEnemyManager.Instance != null ? "✓" : "✗")}";
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    [ShowIf("@UnityEngine.Application.isPlaying && !useCustomValues")]
    private string settingsInfo => settings != null ? $"Using settings: {settings.name}" : "❌ Settings not assigned!";
    
    #if UNITY_EDITOR
    [Button("Test Single Enemy", ButtonSizes.Medium)]
    private void TestSpawnSingleEnemy()
    {
        if (!Application.isPlaying || !enableSpawning || enemyRaceData == null)
        {
            Debug.LogWarning("Cannot test - not in play mode or spawning disabled");
            return;
        }
        
        var availableEnemies = enemyRaceData.GetAvailableEnemyTypes(chunkLevel);
        if (availableEnemies.Length == 0) return;
        
        float totalWeight = enemyRaceData.GetTotalWeight(chunkLevel);
        WeightedEnemyType selectedEnemy = SelectEnemyType(availableEnemies, totalWeight);
        Vector3 spawnPosition = FindValidSpawnPosition();
        
        if (spawnPosition != Vector3.zero && selectedEnemy?.Prefab != null)
        {
            SpawnEnemyAtPosition(selectedEnemy, spawnPosition);
            Debug.Log($"Test spawned {selectedEnemy.Name} at {spawnPosition} using {(useCustomValues ? "custom" : "settings")} values");
        }
    }
    
    [Button("Test Spawn Settings", ButtonSizes.Medium)]
    private void TestSpawnSettings()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test settings in play mode!");
            return;
        }
        
        Debug.Log($"[BreachEnemySpawner] Current Settings:");
        Debug.Log($"  Source: {(useCustomValues ? "Custom Values" : (settings != null ? $"Settings ({settings.name})" : "❌ No Settings!"))}");
        Debug.Log($"  Spawn Interval: {SpawnInterval}s");
        Debug.Log($"  Enemies Per Spawn: {MinEnemiesPerSpawn}-{MaxEnemiesPerSpawn}");
        Debug.Log($"  Max Total Enemies: {MaxSpawnedEnemies}");
        Debug.Log($"  Spawn Radius Factor: {MinSpawnRadiusFactor:F2}-{MaxSpawnRadiusFactor:F2}");
        Debug.Log($"  Spawn Delay: {SpawnDelay}s");
        Debug.Log($"  Particle Type: {SpawnParticleType}");
        Debug.Log($"  Particle to Enemy Delay: {ParticleToEnemyDelay}s");
        Debug.Log($"  Enemy Type: {BreachEnemyType}");
        Debug.Log($"  Allowed Splinters: {AllowedSplinterTypes}");
    }
    #endif
    #endregion
}
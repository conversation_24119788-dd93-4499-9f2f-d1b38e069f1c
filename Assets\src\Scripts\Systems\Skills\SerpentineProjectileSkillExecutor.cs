using UnityEngine;
using UnityEngine.InputSystem;
using System.Collections;

/// <summary>
/// Strategy implementation for serpentine projectile-based skills.
/// Handles serpentine projectile spawning with configurable wave patterns, angular spread, 
/// parallel projectiles, sequential projectile firing, and all support gem effects.
/// Preserves autonomous targeting behavior and maintains zero-GC performance.
/// </summary>
public class SerpentineProjectileSkillExecutor : ISkillExecutor
{
    public bool CanExecute(SkillType skillType) => skillType == SkillType.SerpentineProjectile;

    public void Execute(SkillExecutor skillExecutor, int slotIndex, GemSocketController controller,
                       SkillGemData skillData, Vector3 targetPosition, bool isAutonomous)
    {
        // Check if we need sequential projectiles
        if (skillData.projectileCount > 1 && skillData.projectileDelay > 0f)
        {
            // Start coroutine for sequential projectile firing
            skillExecutor.StartCoroutine(FireSequentialProjectiles(skillExecutor, slotIndex, controller, skillData, targetPosition, isAutonomous));
        }
        else
        {
            // Fire immediately (single projectile or no delay)
            FireProjectileGroup(skillExecutor, slotIndex, controller, skillData, targetPosition, isAutonomous);
        }
    }

    private IEnumerator FireSequentialProjectiles(SkillExecutor skillExecutor, int slotIndex, GemSocketController controller,
                                                  SkillGemData skillData, Vector3 targetPosition, bool isAutonomous)
    {
        // Fire multiple projectiles with delay between each
        for (int i = 0; i < skillData.projectileCount; i++)
        {
            // Update target position for each shot (in case target moved)
            Vector3 currentTargetPosition = isAutonomous ? targetPosition : Camera.main.ScreenToWorldPoint(Mouse.current.position.ReadValue());
            if (!isAutonomous) currentTargetPosition.z = 0f;
            
            FireProjectileGroup(skillExecutor, slotIndex, controller, skillData, currentTargetPosition, isAutonomous);
            
            // Wait before next projectile (except for the last one)
            if (i < skillData.projectileCount - 1)
            {
                yield return new WaitForSeconds(skillData.projectileDelay);
            }
        }
    }

    private void FireProjectileGroup(SkillExecutor skillExecutor, int slotIndex, GemSocketController controller,
                                     SkillGemData skillData, Vector3 targetPosition, bool isAutonomous)
    {
        // Pre-cache all expensive calculations to eliminate repeated allocations
        skillExecutor.CacheSkillExecutionValues(controller, slotIndex);

        // Calculate direction using cached vectors
        var cachedDirectionVector = skillExecutor._cachedDirectionVector;
        cachedDirectionVector = (targetPosition - skillExecutor.transform.position).normalized;
        float baseAngle = Mathf.Atan2(cachedDirectionVector.y, cachedDirectionVector.x) * Mathf.Rad2Deg;

        // Get projectile count and spread (these are relatively cheap)
        int projectileCount = controller.GetTotalProjectileCount();
        bool useParallel = controller.UseParallelProjectiles();
        
        // Access execution cache for cached values
        var executionCache = skillExecutor.GetExecutionCache(slotIndex);
        
        // Spawn multiple projectiles
        for (int i = 0; i < projectileCount; i++)
        {
            Vector3 spawnPosition;
            Quaternion rotation;
            Vector2 direction;

            if (useParallel)
            {
                // Parallel projectiles - calculate lateral offset
                float lateralOffset = controller.GetProjectileLateralOffset();
                float totalWidth = (projectileCount - 1) * lateralOffset;
                float offsetAmount = -totalWidth / 2f + (i * lateralOffset);

                // Calculate perpendicular vector for offset using cached vector
                var cachedPerpendicularVector = skillExecutor._cachedPerpendicularVector;
                cachedPerpendicularVector.Set(-cachedDirectionVector.y, cachedDirectionVector.x);
                cachedPerpendicularVector *= offsetAmount;
                spawnPosition = skillExecutor.transform.position + (Vector3)cachedPerpendicularVector;

                // All parallel projectiles use the same angle and direction
                rotation = Quaternion.Euler(0, 0, baseAngle);
                direction = cachedDirectionVector;
            }
            else
            {
                // Angular spread behavior
                float spreadAngle = controller.GetProjectileSpreadAngle();
                float currentAngle;
                
                if (controller.UseRandomSpread())
                {
                    // Random spread within the spread angle - like Spark in PoE
                    float randomOffset = Random.Range(-spreadAngle / 2f, spreadAngle / 2f);
                    currentAngle = baseAngle + randomOffset;
                }
                else
                {
                    // Even distribution (original behavior)
                    float angleStep = projectileCount > 1 ? spreadAngle : 0;
                    float startAngle = baseAngle - (angleStep * (projectileCount - 1) / 2f);
                    currentAngle = startAngle + (angleStep * i);
                }

                rotation = Quaternion.Euler(0, 0, currentAngle);
                float radians = currentAngle * Mathf.Deg2Rad;
                // Use cached vector for direction calculation
                var cachedVector2Temp = skillExecutor._cachedVector2Temp;
                cachedVector2Temp.Set(Mathf.Cos(radians), Mathf.Sin(radians));
                direction = cachedVector2Temp;
                spawnPosition = skillExecutor.transform.position;
            }
            
            GameObject projectileObj = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, rotation);
            if (projectileObj == null) continue;
            
            // Configure serpentine projectile using cached values
            if (PoolManager.Instance.GetCachedComponent<SerpentineProjectile>(projectileObj, out var serpentineProjectile))
            {
                // Validate cached damage to prevent 0 damage issue
                float finalDamage = executionCache.playerModifiedDamage;
                if (finalDamage <= 0f)
                {
                    // Recalculate if cache is invalid
                    skillExecutor.CacheSkillExecutionValues(controller, slotIndex);
                    executionCache = skillExecutor.GetExecutionCache(slotIndex);
                    finalDamage = executionCache.playerModifiedDamage;
                    
                    if (finalDamage <= 0f)
                    {
                        // Fallback to base damage if still invalid
                        finalDamage = skillData.baseDamage;
                    }
                }

                // Use cached values instead of expensive method calls
                serpentineProjectile.damage = finalDamage;
                serpentineProjectile.lifetime = skillData.duration;
                serpentineProjectile.critChance = executionCache.finalCritChance;
                serpentineProjectile.critMultiplier = executionCache.finalCritMultiplier;
                serpentineProjectile.damageType = executionCache.damageBreakdown.GetPredominantType(); // Use converted type!
                serpentineProjectile.ailmentChance = skillData.ailmentChance;
                serpentineProjectile.damageBreakdown = executionCache.damageBreakdown; // Pass full breakdown!

                // Pass gem data for status effect configuration
                serpentineProjectile.skillGemData = skillData;
                serpentineProjectile.supportGems = skillExecutor.GetCachedSupportGems(controller, slotIndex);

                // Configure serpentine-specific properties from skill data with configurable variation (only for multi-projectile skills)
                if (skillData.intrinsicProjectileCount > 1)
                {
                    float amplitudeVariation = Random.Range(1f - skillData.serpentineAmplitudeVariation, 1f + skillData.serpentineAmplitudeVariation);
                    float frequencyVariation = Random.Range(1f - skillData.serpentineFrequencyVariation, 1f + skillData.serpentineFrequencyVariation);
                    serpentineProjectile.amplitude = skillData.waveAmplitude * amplitudeVariation;
                    serpentineProjectile.frequency = skillData.waveFrequency * frequencyVariation;
                }
                else
                {
                    // Single projectile - use exact values
                    serpentineProjectile.amplitude = skillData.waveAmplitude;
                    serpentineProjectile.frequency = skillData.waveFrequency;
                }
                serpentineProjectile.delay = skillData.serpentineDelay;

                // Apply support gem effects using cached values
                if (executionCache.hasPierce)
                {
                    serpentineProjectile.SetPiercing(true);
                }

                if (executionCache.hasChain)
                {
                    serpentineProjectile.SetChaining(true, executionCache.chainCount);
                }

                if (executionCache.hasFork)
                {
                    serpentineProjectile.SetFork(true, executionCache.forkCount, executionCache.forkAngle);
                }

                if (executionCache.hasAreaDamage)
                {
                    serpentineProjectile.SetAreaDamage(true, executionCache.areaRadius);
                }

                // Initialize projectile with cached damage (pass actual damage value, not multiplier)
                CollisionLayers layer = skillData.projectileLayer; // Use gem's collision layer
                serpentineProjectile.Initialize((Vector2)spawnPosition, direction, finalDamage, layer, skillData.projectileSpeed, skillData.duration);
                
                // Apply speed variation AFTER Initialize() to prevent override (only for multi-projectile skills)
                if (skillData.intrinsicProjectileCount > 1 && skillData.projectileSpeedVariation > 0f)
                {
                    float speedVariation = Random.Range(-skillData.projectileSpeedVariation, skillData.projectileSpeedVariation);
                    serpentineProjectile.speed = Mathf.Max(1f, skillData.projectileSpeed + speedVariation);
                }
                
                // Double-check damage after initialization
                if (serpentineProjectile.damage <= 0f)
                {
                    Debug.LogError($"[SerpentineSkillExecutor] Projectile damage is 0 after initialization! Setting to fallback: {skillData.baseDamage}");
                    serpentineProjectile.damage = skillData.baseDamage;
                }
            }
            // Fallback to regular Projectile component if SerpentineProjectile is not found
            else if (PoolManager.Instance.GetCachedComponent<Projectile>(projectileObj, out var regularProjectile))
            {
                Debug.LogWarning($"[SerpentineSkillExecutor] SerpentineProjectile component not found on {skillData.skillPrefab.name}, falling back to regular Projectile");
                
                // Configure as regular projectile
                float finalDamage = executionCache.playerModifiedDamage;
                if (finalDamage <= 0f)
                {
                    finalDamage = skillData.baseDamage;
                }

                regularProjectile.damage = finalDamage;
                regularProjectile.speed = skillData.projectileSpeed;
                regularProjectile.lifetime = skillData.duration;
                regularProjectile.critChance = executionCache.finalCritChance;
                regularProjectile.critMultiplier = executionCache.finalCritMultiplier;
                regularProjectile.damageType = executionCache.damageBreakdown.GetPredominantType();
                regularProjectile.ailmentChance = skillData.ailmentChance;
                regularProjectile.damageBreakdown = executionCache.damageBreakdown;
                regularProjectile.skillGemData = skillData;
                regularProjectile.supportGems = skillExecutor.GetCachedSupportGems(controller, slotIndex);

                // Apply support gem effects
                if (executionCache.hasPierce)
                {
                    regularProjectile.SetPiercing(true);
                }

                if (executionCache.hasChain)
                {
                    regularProjectile.SetChaining(true, executionCache.chainCount);
                }

                if (executionCache.hasFork)
                {
                    regularProjectile.SetFork(true, executionCache.forkCount, executionCache.forkAngle);
                }

                if (executionCache.hasAreaDamage)
                {
                    regularProjectile.SetAreaDamage(true, executionCache.areaRadius);
                }

                CollisionLayers regularLayer = skillData.projectileLayer; // Use gem's collision layer
                regularProjectile.Initialize((Vector2)spawnPosition, direction, finalDamage, regularLayer, skillData.projectileSpeed, skillData.duration);
            }
        }
    }
}
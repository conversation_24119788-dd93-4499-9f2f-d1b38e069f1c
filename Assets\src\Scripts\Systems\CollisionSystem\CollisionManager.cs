using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Collision detection system mode for switching between custom and Physics2D detection
/// </summary>
public enum CollisionDetectionMode
{
    /// <summary>
    /// Use custom geometric collision detection (current system)
    /// </summary>
    Custom,

    /// <summary>
    /// Use Unity Physics2D collision detection with raycasting
    /// </summary>
    Physics2D
}

public class CollisionManager : MonoBehaviour
{
        [Title("Collision Settings")]

        [SerializeField]
        [InfoBox("Choose collision detection method. Physics2D provides more reliable collision detection.", InfoMessageType.Info)]
        private CollisionDetectionMode collisionMode = CollisionDetectionMode.Custom;

        [SerializeField, ShowIf("collisionMode", CollisionDetectionMode.Physics2D)]
        [Required, Tooltip("Layer mapping for Physics2D collision detection")]
        private Physics2DLayerMapping physics2DLayerMapping;

        [SerializeField]
        [Required]
        private TilemapChunkManager chunkManager;
        
        // OPTIMIZED SINGLE-CHUNK MODE: Process only the current player chunk with neighbor buffer for cleanup
        // neighbourChunkRange is effectively ignored in optimized mode
        [Serial<PERSON>Field, MinValue(0)]
        [InfoBox("OPTIMIZED MODE: Only current player chunk is processed, with 1-chunk cleanup buffer to prevent collision failures", InfoMessageType.Info)]
        private int neighbourChunkRange = 0;
        
        [Title("Spatial Settings")]
        [SerializeField, Range(0.5f, 4f)]
        [Tooltip("Cell size for spatial grids in each chunk")]
        private float spatialGridCellSize = 2f;
        
        [SerializeField]
        [Tooltip("Enable debug logging for chunk collision operations")]
        private bool debugChunkCollisions = true;
        
        [Title("Chunk Processing Presets")]
        [SerializeField]
        [InfoBox("Choose a preset configuration optimized for your game type")]
        private ChunkProcessingPreset chunkPreset = ChunkProcessingPreset.SingleChunk;
        
        
        [Title("Chunk Performance Info")]
        [ShowInInspector, ReadOnly]
        [InfoBox("OPTIMIZED MODE: Always processes exactly 1 chunk (current player chunk)", InfoMessageType.Info)]
        private int chunksBeingProcessed => 1; // Always 1 in optimized mode
        
        [ShowInInspector, ReadOnly]
        [LabelText("Performance Multiplier")]
        private string performanceMultiplier => "1x (OPTIMIZED SINGLE-CHUNK MODE)";
        
        [Title("Debug")]
        [ShowInInspector, ReadOnly]
        private int activeCollidersCount;
        
        [ShowInInspector, ReadOnly]
        private ChunkCoordinate currentChunk;
        
        // Debug event toggles
        [Title("Debug Events")]
        [SerializeField]
        private bool logCollisionEvents = true;
        
        [SerializeField]
        private bool logTriggerEvents = true;
        
        // Public accessors for other classes
        public bool LogCollisionEvents => logCollisionEvents;
        public bool LogTriggerEvents => logTriggerEvents;
        
        public static CollisionManager Instance { get; private set; }
        
        // Integrated spatial collision system (no longer delegated)
        [Title("Integrated Spatial System")]
        [ShowInInspector, ReadOnly]
        [InfoBox("Chunk-based collision tracking data")]
        private Dictionary<ChunkCoordinate, ChunkCollisionData> chunkCollisions;
        
        [ShowInInspector, ReadOnly]
        [InfoBox("Active chunks currently being processed for collisions")]
        private readonly HashSet<ChunkCoordinate> activeChunks = new HashSet<ChunkCoordinate>();
        
        [ShowInInspector, ReadOnly]
        [InfoBox("Maps objects to their current chunk for quick lookup")]
        private readonly Dictionary<ICollidable, ChunkCoordinate> objectToChunk = new Dictionary<ICollidable, ChunkCoordinate>();
        
        [Title("Performance Buffers")]
        [ShowInInspector, ReadOnly]
        [InfoBox("Reusable buffers to avoid allocations during collision processing")]
        private readonly List<ICollidable> nearbyObjectsBuffer = new List<ICollidable>(32);
        
        [ShowInInspector, ReadOnly]
        private readonly List<ChunkCoordinate> neighborChunksBuffer = new List<ChunkCoordinate>(8);
        
        // Cached chunk coordinate for position queries to avoid allocations
        private ChunkCoordinate cachedChunkCoord;
        
        // Separate cached coordinate for radius queries to prevent race conditions
        private ChunkCoordinate cachedRadiusQueryCoord;
        
        // Track last player chunk to detect chunk changes
        private ChunkCoordinate lastPlayerChunk = new ChunkCoordinate(int.MinValue, int.MinValue);
        
        // Reusable buffers to prevent allocations during cleanup (CRITICAL FIX)
        private readonly List<ChunkCoordinate> _chunksToRemoveBuffer = new List<ChunkCoordinate>();
        private readonly List<ICollidable> _objectsToRemoveBuffer = new List<ICollidable>();
        private readonly List<ICollidable> _activeCollidables = new List<ICollidable>();
        // B1: Removed global collision state - _currentCollisions and _hashSetPool deleted
        private readonly Queue<CollisionInfo> _collisionInfoPool = new Queue<CollisionInfo>();
        private int _maxPoolSize = 4000;
        private int _currentPoolSize = 0;
        // B1: Global HashSet pool removed - chunks now manage their own pools
        
        // Registration throttling to prevent startup overload
        [Title("Registration Throttling")]
        [SerializeField, Range(10, 200)]
        [Tooltip("Maximum number of new registrations per frame during startup")]
        private int maxRegistrationsPerFrame = 50;
        
        [SerializeField]
        [Tooltip("Enable registration throttling to prevent startup overload")]
        private bool enableRegistrationThrottling = true;
        
        [ShowInInspector, ReadOnly]
        private int _registrationsThisFrame = 0;
        
        [ShowInInspector, ReadOnly]
        private int _pendingRegistrations => _registrationQueue.Count;
        
        // Queue for throttled registrations
        private readonly Queue<ICollidable> _registrationQueue = new Queue<ICollidable>();
        private readonly HashSet<ICollidable> _pendingRegistrationSet = new HashSet<ICollidable>(); // Prevent duplicates

        // Pooled collections to reduce GC pressure
        
        // Cached Vector2 instances to avoid allocations
        private Vector2 _cachedDiff;
        private Vector2 _cachedNormal;
        private Vector2 _cachedContactPoint;
        private Vector2 _cachedMin;
        private Vector2 _cachedMax;
        private Vector2 _cachedClosest;
        private Vector2 _cachedBoxMin;
        private Vector2 _cachedBoxMax;
        private Vector2 _cachedPosition;
        
        // B1: Collision keys snapshot removed - no global collision state to iterate
        
        // Buffer for radius queries to avoid allocations
        private readonly List<ICollidable> chunkObjectsBuffer = new List<ICollidable>(64);
        
        // Debug counters for pool management
        [ShowInInspector, ReadOnly]
        private int _poolHits = 0;
        [ShowInInspector, ReadOnly]
        private int _poolMisses = 0;
        [ShowInInspector, ReadOnly]
        private int _currentPoolCount = 0;
        
        // B1: Global HashSet pool debug information removed - chunks manage their own pools
        [ShowInInspector, ReadOnly]
        private int _registeredCollidables => _activeCollidables.Count;
        
        [ShowInInspector, ReadOnly]
        [LabelText("Total Chunks With Collisions")]
        public int TotalChunksWithCollisions => chunkCollisions?.Count ?? 0;
        
        [ShowInInspector, ReadOnly]
        [LabelText("Active Chunk Count")]
        public int ActiveChunkCount => activeChunks.Count;
        
        [ShowInInspector, ReadOnly]
        [LabelText("Tracked Objects in Spatial System")]
        public int TrackedObjectCount => objectToChunk.Count;
        // B1: Global collision tracking count removed - use chunk-based tracking instead
        
        private ChunkCoordinate _lastPlayerChunk = new ChunkCoordinate(int.MinValue, int.MinValue);
        private Vector2 _currentChunkMin;
        private Vector2 _currentChunkMax;
        
        // Debug timer for periodic HashSet pool monitoring
        private float _lastPoolDebugTime = 0f;
        private const float POOL_DEBUG_INTERVAL = 5f; // Check every 5 seconds
        private const int POOL_WARNING_THRESHOLD = 400; // Warn when 80% of pool is in use
        
        // Memory pressure monitoring
        [Title("Memory Pressure Monitoring")]
        [ShowInInspector, ReadOnly]
        [InfoBox("Memory pressure tracking to prevent allocation spikes")]
        private long _lastGcMemory = 0;
        
        [ShowInInspector, ReadOnly]
        private int _gcCollectionCount = 0;
        
        [ShowInInspector, ReadOnly]
        private float _memoryPressureScore = 0f; // 0-1 scale, higher = more pressure
        
        [ShowInInspector, ReadOnly]
        private int _emergencyPoolExpansions = 0;
        
        private float _lastMemoryCheckTime = 0f;
        private const float MEMORY_CHECK_INTERVAL = 2f; // Check every 2 seconds
        private const float HIGH_PRESSURE_THRESHOLD = 0.7f; // Trigger warnings above this
        
        // Startup monitoring
        [Title("Startup Monitoring")]
        [ShowInInspector, ReadOnly]
        private bool _isStartupPhase = true;
        [ShowInInspector, ReadOnly]
        private float _startupTime = 0f;
        [ShowInInspector, ReadOnly]
        private int _startupRegistrations = 0;
        [ShowInInspector, ReadOnly]
        private int _maxSimultaneousRegistrations = 0;
        private const float STARTUP_PHASE_DURATION = 10f; // Consider first 10 seconds as startup
        
        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;
            
            // Initialize spatial collision system directly (no component lookup)
            InitializeSpatialCollisionSystem();
            
            // Synchronize preset with current neighbourChunkRange value
            SynchronizePresetWithChunkRange();
            
            // Validate required components and configuration
            ValidateSystemConfiguration();
            
            // Pre-pool collision info objects for massive projectile scenarios
            InitializeCollisionInfoPool(4000);
        }
        
        /// <summary>
        /// Initialize the integrated spatial collision system
        /// </summary>
        private void InitializeSpatialCollisionSystem()
        {
            chunkCollisions = new Dictionary<ChunkCoordinate, ChunkCollisionData>();
            
            if (chunkManager == null)
            {
                chunkManager = TilemapChunkManager.Instance;
            }

            // Initialize Physics2D collision utilities if using Physics2D mode
            if (collisionMode == CollisionDetectionMode.Physics2D)
            {
                InitializePhysics2DSystem();
            }
        }
        
        public void RegisterCollidable(ICollidable collidable)
        {
            if (collidable == null || _activeCollidables.Contains(collidable)) return;
            
            // If registration throttling is enabled and we're over the limit this frame
            if (enableRegistrationThrottling && _registrationsThisFrame >= maxRegistrationsPerFrame)
            {
                // Add to queue if not already pending
                if (!_pendingRegistrationSet.Contains(collidable))
                {
                    _registrationQueue.Enqueue(collidable);
                    _pendingRegistrationSet.Add(collidable);
                    
                }
                return;
            }
            
            // Perform immediate registration
            PerformRegistration(collidable);
            _registrationsThisFrame++;
        }
        
        private void PerformRegistration(ICollidable collidable)
        {
            _activeCollidables.Add(collidable);
            // B1: Global collision tracking removed - chunks handle their own collision state

            // Track startup statistics
            if (_isStartupPhase)
            {
                _startupRegistrations++;
                _maxSimultaneousRegistrations = Mathf.Max(_maxSimultaneousRegistrations, _activeCollidables.Count);
            }

            // Ensure Collider2D components are set up for Physics2D mode
            if (collisionMode == CollisionDetectionMode.Physics2D)
            {
                EnsurePhysics2DComponents(collidable);
            }

            // Directly register in spatial system
            RegisterCollidableInSpatialSystem(collidable);

        }
        
        public void UnregisterCollidable(ICollidable collidable)
        {
            if (collidable == null) return;
            
            // Remove from pending queue if it's there
            if (_pendingRegistrationSet.Contains(collidable))
            {
                _pendingRegistrationSet.Remove(collidable);
                // Note: We can't efficiently remove from Queue, but it will be skipped during processing
            }
            
            _activeCollidables.Remove(collidable);
            
            // Directly unregister from spatial system
            UnregisterCollidableFromSpatialSystem(collidable);
            
            // B1: Global collision cleanup removed - chunks handle their own exit events
            
        }
        
        public void UpdateCollidablePosition(ICollidable collidable)
        {
            // Directly update in spatial system
            UpdateCollidablePositionInSpatialSystem(collidable);
        }
        
        /// <summary>
        /// Get all collidables within a radius using spatial optimization.
        /// This is a zero-GC method that fills the provided results list.
        /// </summary>
        /// <param name="position">World position to query from</param>
        /// <param name="radius">Search radius</param>
        /// <param name="results">List to fill with results (will be cleared first)</param>
        /// <param name="layerMask">Layer filter for results</param>
        public void GetCollidersInRadiusNonAlloc(Vector2 position, float radius, List<ICollidable> results, CollisionLayers layerMask = CollisionLayers.All)
        {
            results.Clear();
            
            if (chunkManager == null) 
            {
                return;
            }
            
            // Calculate which chunks the radius could intersect
            float chunkWidth = chunkManager.GetChunkWidth();
            float chunkHeight = chunkManager.GetChunkHeight();
            
            // Calculate chunk bounds that could contain objects within radius
            int minChunkX = Mathf.FloorToInt((position.x - radius) / chunkWidth);
            int maxChunkX = Mathf.FloorToInt((position.x + radius) / chunkWidth);
            int minChunkY = Mathf.FloorToInt((position.y - radius) / chunkHeight);
            int maxChunkY = Mathf.FloorToInt((position.y + radius) / chunkHeight);
            
            // Query each potentially intersecting chunk
            _cachedPosition = position;
            float radiusSquared = radius * radius;
            
            for (int chunkX = minChunkX; chunkX <= maxChunkX; chunkX++)
            {
                for (int chunkY = minChunkY; chunkY <= maxChunkY; chunkY++)
                {
                    cachedRadiusQueryCoord.x = chunkX;
                    cachedRadiusQueryCoord.y = chunkY;
                    
                    // Check if this chunk has collision data
                    if (TryGetChunkData(cachedRadiusQueryCoord, out var chunkData))
                    {
                        // Query the spatial grid within this chunk
                        chunkObjectsBuffer.Clear();
                        chunkData.LocalGrid.GetObjectsInRadius(_cachedPosition, radius, chunkObjectsBuffer);
                        
                        // Filter by distance, layer, and active state
                        for (int i = 0; i < chunkObjectsBuffer.Count; i++)
                        {
                            var collidable = chunkObjectsBuffer[i];
                            if (!collidable.IsActive) continue;
                            
                            // Layer filtering
                            if (layerMask != CollisionLayers.All && !layerMask.HasFlag(collidable.Layer)) continue;
                            
                            // Distance check (precise)
                            _cachedDiff.x = collidable.Position.x - _cachedPosition.x;
                            _cachedDiff.y = collidable.Position.y - _cachedPosition.y;
                            float distanceSquared = _cachedDiff.x * _cachedDiff.x + _cachedDiff.y * _cachedDiff.y;
                            
                            if (distanceSquared <= radiusSquared)
                            {
                                results.Add(collidable);
                            }
                        }
                    }
                }
            }
        }
        
        private void FixedUpdate()
        {
            // Update startup phase tracking
            if (_isStartupPhase)
            {
                _startupTime += Time.fixedDeltaTime;
                if (_startupTime >= STARTUP_PHASE_DURATION)
                {
                    _isStartupPhase = false;
                    LogStartupSummary();
                }
            }
            
            // Reset frame counter
            _registrationsThisFrame = 0;
            
            // Process pending registrations
            ProcessPendingRegistrations();
            
            UpdateCurrentChunk();
            ProcessCollisions();
            activeCollidersCount = _activeCollidables.Count;
            _currentPoolCount = _collisionInfoPool.Count;
            
            // B6: HashSet pool monitoring removed - chunks manage their own pools
            
            // Memory pressure monitoring
            CheckMemoryPressure();
        }
        
        private void ProcessPendingRegistrations()
        {
            if (!enableRegistrationThrottling) return;
            
            int processed = 0;
            while (_registrationQueue.Count > 0 && processed < maxRegistrationsPerFrame)
            {
                var collidable = _registrationQueue.Dequeue();
                
                // Check if it's still valid and not already registered
                if (collidable != null && !_activeCollidables.Contains(collidable))
                {
                    // Remove from pending set
                    _pendingRegistrationSet.Remove(collidable);
                    
                    // Perform the registration
                    PerformRegistration(collidable);
                    processed++;
                    _registrationsThisFrame++;
                }
                else
                {
                    // Remove invalid/duplicate entries from pending set
                    _pendingRegistrationSet.Remove(collidable);
                }
            }
            
        }
        
        private void LogStartupSummary()
        {
            // Debug logging removed - method kept for compatibility
        }
        
        private void UpdateCurrentChunk()
        {
            if (chunkManager == null) return;
            
            var playerChunk = chunkManager.GetCurrentPlayerChunk();
            if (playerChunk != _lastPlayerChunk)
            {
                _lastPlayerChunk = playerChunk;
                currentChunk = playerChunk;
                
                // Calculate extended bounds that encompass the neighbouring chunks defined by neighbourChunkRange
                var chunkWidth = chunkManager.GetChunkWidth();
                var chunkHeight = chunkManager.GetChunkHeight();

                // OPTIMIZATION: Single-chunk mode (neighbourChunkRange = 0) - minimal boundary calculation
                if (neighbourChunkRange == 0)
                {
                    // Process only the exact current chunk - no boundary expansion
                    _currentChunkMin = new Vector2(playerChunk.x * chunkWidth, playerChunk.y * chunkHeight);
                    _currentChunkMax = new Vector2((playerChunk.x + 1) * chunkWidth, (playerChunk.y + 1) * chunkHeight);
                }
                else
                {
                    // Multi-chunk mode - expand boundaries to include neighbors
                    _currentChunkMin = new Vector2(
                        (playerChunk.x - neighbourChunkRange) * chunkWidth,
                        (playerChunk.y - neighbourChunkRange) * chunkHeight);

                    _currentChunkMax = new Vector2(
                        (playerChunk.x + 1 + neighbourChunkRange) * chunkWidth,
                        (playerChunk.y + 1 + neighbourChunkRange) * chunkHeight);
                }
                
                
                // B1: Global collision clearing removed - chunks handle their own collision state
            }
        }
        
        private bool IsInCurrentChunk(Vector2 position)
        {
            return position.x >= _currentChunkMin.x && position.x < _currentChunkMax.x &&
                   position.y >= _currentChunkMin.y && position.y < _currentChunkMax.y;
        }
        
        private void ProcessCollisions()
        {
            ProcessChunkCollisions();
        }
        
        // =============================================================================
        // INTEGRATED SPATIAL COLLISION SYSTEM METHODS
        // =============================================================================
        
        /// <summary>
        /// Process collisions for all active chunks using spatial optimization
        /// </summary>
        private void ProcessChunkCollisions()
        {
            if (chunkManager == null) return;
            
            UpdateActiveChunks();
            ProcessActiveChunkCollisions();
        }
        
        /// <summary>
        /// Update the list of active chunks based on player position using zero-GC pattern.
        /// OPTIMIZED SINGLE-CHUNK MODE: Only process the current player chunk, ignore all others.
        /// </summary>
        private void UpdateActiveChunks()
        {
            var playerChunk = chunkManager.GetCurrentPlayerChunk();
            
            // Check if player changed chunks
            if (playerChunk != lastPlayerChunk)
            {
                // Aggressive cleanup of ALL non-current chunks
                CleanupAllNonCurrentChunks(playerChunk);
                lastPlayerChunk = playerChunk;
                
            }
            
            // OPTIMIZED: Only the current player chunk is active
            activeChunks.Clear();
            activeChunks.Add(playerChunk);
            
        }
        
        /// <summary>
        /// Clean up chunks that are far from the current player chunk.
        /// OPTIMIZED SINGLE-CHUNK MODE: Remove chunks that are more than 1 chunk away to prevent
        /// aggressive cleanup that breaks collision detection when player moves between chunks.
        /// Uses reusable buffers to prevent allocations during cleanup.
        /// </summary>
        private void CleanupAllNonCurrentChunks(ChunkCoordinate currentPlayerChunk)
        {
            if (chunkCollisions == null) return;
            
            // Use reusable buffers to prevent allocations
            _chunksToRemoveBuffer.Clear();
            _objectsToRemoveBuffer.Clear();
            
            // Find chunks that are more than 1 chunk away from current player chunk
            // This provides a small buffer to prevent aggressive cleanup that breaks collision detection
            foreach (var kvp in chunkCollisions)
            {
                var chunkCoord = kvp.Key;
                int deltaX = Mathf.Abs(chunkCoord.x - currentPlayerChunk.x);
                int deltaY = Mathf.Abs(chunkCoord.y - currentPlayerChunk.y);
                
                // Only remove chunks that are more than 1 chunk away in both directions
                // This keeps immediate neighbors to prevent collision system failures during chunk transitions
                if (deltaX > 1 || deltaY > 1)
                {
                    _chunksToRemoveBuffer.Add(chunkCoord);
                }
            }
            
            // Remove all non-current chunks immediately
            foreach (var chunkCoord in _chunksToRemoveBuffer)
            {
                if (chunkCollisions.TryGetValue(chunkCoord, out var chunkData))
                {
                    // Clear all collision data and return pooled objects
                    chunkData.Clear();
                    chunkCollisions.Remove(chunkCoord);

                }
            }

            // Clear Physics2D caches when chunks are cleaned up
            if (collisionMode == CollisionDetectionMode.Physics2D && _chunksToRemoveBuffer.Count > 0)
            {
                Physics2DCollisionUtilities.ClearCaches();
            }
            
            // Clean up object-to-chunk mappings for objects not in current chunk
            foreach (var kvp in objectToChunk)
            {
                if (kvp.Value != currentPlayerChunk)
                {
                    _objectsToRemoveBuffer.Add(kvp.Key);
                }
            }
            
            foreach (var obj in _objectsToRemoveBuffer)
            {
                objectToChunk.Remove(obj);
            }
            
        }
        
        /// <summary>
        /// Process collision detection for all active chunks
        /// </summary>
        private void ProcessActiveChunkCollisions()
        {
            int chunksProcessed = 0;
            int objectsProcessed = 0;
            
            foreach (var chunkCoord in activeChunks)
            {
                if (chunkCollisions.TryGetValue(chunkCoord, out var chunkData))
                {
                    if (chunkData.ObjectCount > 0)
                    {
                        ProcessSingleChunkCollisions(chunkCoord, chunkData);
                        chunksProcessed++;
                        objectsProcessed += chunkData.ObjectCount;
                    }
                }
            }
            
        }
        
        /// <summary>
        /// Process collision detection for a specific chunk
        /// </summary>
        /// <param name="chunkCoord">The chunk coordinate to process</param>
        /// <param name="chunkData">The chunk's collision data</param>
        private void ProcessSingleChunkCollisions(ChunkCoordinate chunkCoord, ChunkCollisionData chunkData)
        {
            // Process only objects in this chunk
            for (int i = 0; i < chunkData.Objects.Count; i++)
            {
                var collidable = chunkData.Objects[i];
                if (!collidable.IsActive) continue;
                
                // Use local spatial grid for nearby queries
                nearbyObjectsBuffer.Clear();
                chunkData.LocalGrid.GetNearbyNonAlloc(collidable, nearbyObjectsBuffer);
                
                ProcessCollisionPairs(collidable, nearbyObjectsBuffer, chunkData);
            }
        }
        
        /// <summary>
        /// Process collision pairs for a specific object using the collision manager's logic
        /// </summary>
        /// <param name="collidable">The object to check collisions for</param>
        /// <param name="nearbyObjects">List of nearby objects to check against</param>
        /// <param name="chunkData">The chunk data for collision tracking</param>
        private void ProcessCollisionPairs(ICollidable collidable, List<ICollidable> nearbyObjects, ChunkCollisionData chunkData)
        {
            if (!chunkData.CollisionPairs.TryGetValue(collidable, out var previousCollisions))
            {
                previousCollisions = chunkData.GetPooledHashSet();
                chunkData.CollisionPairs[collidable] = previousCollisions;
            }
            
            var currentCollisions = chunkData.GetPooledHashSet();
            
            // Check collisions with nearby objects
            for (int i = 0; i < nearbyObjects.Count; i++)
            {
                var other = nearbyObjects[i];
                if (!CanCollide(collidable, other)) continue;
                
                if (CheckCollision(collidable, other, out var info))
                {
                    currentCollisions.Add(other);
                    
                    bool wasColliding = previousCollisions.Contains(other);
                    
                    // Fire appropriate collision events
                    if (collidable.IsTrigger || other.IsTrigger)
                    {
                        if (!wasColliding)
                        {
                            collidable.OnSpatialTriggerEnter(info);
                            SetupReverseCollisionInfo(info, collidable);
                            other.OnSpatialTriggerEnter(info);
                        }
                        else
                        {
                            collidable.OnSpatialTriggerStay(info);
                            SetupReverseCollisionInfo(info, collidable);
                            other.OnSpatialTriggerStay(info);
                        }
                    }
                    else
                    {
                        if (!wasColliding)
                        {
                            collidable.OnSpatialCollisionEnter(info);
                            SetupReverseCollisionInfo(info, collidable);
                            other.OnSpatialCollisionEnter(info);
                        }
                        else
                        {
                            collidable.OnSpatialCollisionStay(info);
                            SetupReverseCollisionInfo(info, collidable);
                            other.OnSpatialCollisionStay(info);
                        }
                    }
                    
                    // Return collision info to manager's pool
                    ReturnCollisionInfo(info);
                }
            }
            
            // Check for collision exits
            foreach (var previousCollision in previousCollisions)
            {
                if (!currentCollisions.Contains(previousCollision))
                {
                    var info = GetCollisionInfo();
                    info.Other = previousCollision;
                    
                    if (collidable.IsTrigger || previousCollision.IsTrigger)
                    {
                        collidable.OnSpatialTriggerExit(info);
                        SetupReverseCollisionInfo(info, collidable);
                        previousCollision.OnSpatialTriggerExit(info);
                    }
                    else
                    {
                        collidable.OnSpatialCollisionExit(info);
                        SetupReverseCollisionInfo(info, collidable);
                        previousCollision.OnSpatialCollisionExit(info);
                    }
                    
                    ReturnCollisionInfo(info);
                }
            }
            
            // Update collision tracking
            chunkData.ReturnHashSetToPool(previousCollisions);
            chunkData.CollisionPairs[collidable] = currentCollisions;
        }
        
        /// <summary>
        /// Register a collidable object in the appropriate chunk
        /// </summary>
        /// <param name="collidable">Object to register</param>
        private void RegisterCollidableInSpatialSystem(ICollidable collidable)
        {
            if (collidable == null) return;
            
            var chunkCoord = GetChunkForPosition(collidable.Position);
            bool chunkExisted = chunkCollisions.ContainsKey(chunkCoord);
            
            // Create chunk data if it doesn't exist
            if (!chunkCollisions.TryGetValue(chunkCoord, out var chunkData))
            {
                chunkData = new ChunkCollisionData(chunkCoord, spatialGridCellSize);
                chunkCollisions[chunkCoord] = chunkData;
                
            }
            
            // Add object to chunk
            chunkData.AddObject(collidable);
            objectToChunk[collidable] = chunkCoord;
            
        }
        
        /// <summary>
        /// Unregister a collidable object from chunk tracking
        /// </summary>
        /// <param name="collidable">Object to unregister</param>
        private void UnregisterCollidableFromSpatialSystem(ICollidable collidable)
        {
            if (collidable == null || !objectToChunk.TryGetValue(collidable, out var chunkCoord)) return;
            
            bool chunkRemoved = false;
            if (chunkCollisions.TryGetValue(chunkCoord, out var chunkData))
            {
                int objectCountBefore = chunkData.ObjectCount;
                chunkData.RemoveObject(collidable);
                
                // Clean up empty chunk data
                if (chunkData.ObjectCount == 0)
                {
                    chunkData.Clear();
                    chunkCollisions.Remove(chunkCoord);
                    chunkRemoved = true;
                }
                
            }
            
            objectToChunk.Remove(collidable);
        }
        
        /// <summary>
        /// Update an object's position in chunk tracking
        /// </summary>
        /// <param name="collidable">Object to update</param>
        private void UpdateCollidablePositionInSpatialSystem(ICollidable collidable)
        {
            if (collidable == null || !objectToChunk.TryGetValue(collidable, out var oldChunk)) return;
            
            var newChunk = GetChunkForPosition(collidable.Position);
            
            if (oldChunk == newChunk)
            {
                // Same chunk - just update spatial grid
                if (chunkCollisions.TryGetValue(oldChunk, out var chunkData))
                {
                    chunkData.UpdateObjectPosition(collidable);
                }
            }
            else
            {
                // Moved to different chunk - re-register
                UnregisterCollidableFromSpatialSystem(collidable);
                RegisterCollidableInSpatialSystem(collidable);
            }
        }
        
        /// <summary>
        /// Get the chunk coordinate for a world position using zero-GC ref parameter pattern
        /// </summary>
        /// <param name="position">World position</param>
        /// <param name="result">Output chunk coordinate to avoid struct allocation</param>
        private void GetChunkForPosition(Vector2 position, ref ChunkCoordinate result)
        {
            var chunkWidth = chunkManager.GetChunkWidth();
            var chunkHeight = chunkManager.GetChunkHeight();
            
            result.x = Mathf.FloorToInt(position.x / chunkWidth);
            result.y = Mathf.FloorToInt(position.y / chunkHeight);
        }
        
        /// <summary>
        /// Get the chunk coordinate for a world position (legacy method for compatibility)
        /// </summary>
        /// <param name="position">World position</param>
        /// <returns>Chunk coordinate</returns>
        private ChunkCoordinate GetChunkForPosition(Vector2 position)
        {
            GetChunkForPosition(position, ref cachedChunkCoord);
            return cachedChunkCoord;
        }
        
        /// <summary>
        /// Try to get chunk data for a specific chunk coordinate
        /// </summary>
        /// <param name="chunkCoord">Chunk coordinate to query</param>
        /// <param name="chunkData">Output chunk data if found</param>
        /// <returns>True if chunk data exists</returns>
        public bool TryGetChunkData(ChunkCoordinate chunkCoord, out ChunkCollisionData chunkData)
        {
            return chunkCollisions.TryGetValue(chunkCoord, out chunkData);
        }
        
        // =============================================================================
        // END INTEGRATED SPATIAL COLLISION SYSTEM METHODS
        // =============================================================================
        
        private bool CanCollide(ICollidable a, ICollidable b)
        {
            return CollisionLayerMatrix.CanLayersCollide(a.Layer, b.Layer);
        }
        
        // Fast collision detection methods - no CollisionInfo allocation
        private bool FastCircleVsCircleCheck(ICollidable a, ICollidable b)
        {
            float dx = b.Position.x - a.Position.x;
            float dy = b.Position.y - a.Position.y;
            float distSq = dx * dx + dy * dy;
            float radiusSum = a.Radius + b.Radius;
            return distSq < radiusSum * radiusSum;
        }
        
        private bool FastBoxVsBoxCheck(ICollidable a, ICollidable b)
        {
            float aHalfSizeX = a.Size.x * 0.5f;
            float aHalfSizeY = a.Size.y * 0.5f;
            float bHalfSizeX = b.Size.x * 0.5f;
            float bHalfSizeY = b.Size.y * 0.5f;
            
            float aMinX = a.Position.x - aHalfSizeX;
            float aMaxX = a.Position.x + aHalfSizeX;
            float aMinY = a.Position.y - aHalfSizeY;
            float aMaxY = a.Position.y + aHalfSizeY;
            
            float bMinX = b.Position.x - bHalfSizeX;
            float bMaxX = b.Position.x + bHalfSizeX;
            float bMinY = b.Position.y - bHalfSizeY;
            float bMaxY = b.Position.y + bHalfSizeY;
            
            return aMinX < bMaxX && aMaxX > bMinX && aMinY < bMaxY && aMaxY > bMinY;
        }
        
        private bool FastCircleVsBoxCheck(ICollidable circle, ICollidable box)
        {
            float boxHalfSizeX = box.Size.x * 0.5f;
            float boxHalfSizeY = box.Size.y * 0.5f;
            
            float boxMinX = box.Position.x - boxHalfSizeX;
            float boxMaxX = box.Position.x + boxHalfSizeX;
            float boxMinY = box.Position.y - boxHalfSizeY;
            float boxMaxY = box.Position.y + boxHalfSizeY;
            
            // Find closest point on box to circle center
            float closestX = Mathf.Clamp(circle.Position.x, boxMinX, boxMaxX);
            float closestY = Mathf.Clamp(circle.Position.y, boxMinY, boxMaxY);
            
            float dx = circle.Position.x - closestX;
            float dy = circle.Position.y - closestY;
            float distSq = dx * dx + dy * dy;
            
            return distSq < circle.Radius * circle.Radius;
        }
        
        public bool CheckCollision(ICollidable a, ICollidable b, out CollisionInfo info)
        {
            // Route to appropriate collision detection method based on mode
            switch (collisionMode)
            {
                case CollisionDetectionMode.Custom:
                    return CheckCollisionCustom(a, b, out info);

                case CollisionDetectionMode.Physics2D:
                    return CheckCollisionPhysics2D(a, b, out info);

                default:
                    info = null;
                    return false;
            }
        }

        /// <summary>
        /// Custom geometric collision detection (original implementation)
        /// </summary>
        private bool CheckCollisionCustom(ICollidable a, ICollidable b, out CollisionInfo info)
        {
            // Fast geometric check first - don't get CollisionInfo until we know collision occurred
            bool collisionDetected = false;

            // Circle vs Circle
            if (a.Shape == ColliderShape.Circle && b.Shape == ColliderShape.Circle)
            {
                collisionDetected = FastCircleVsCircleCheck(a, b);
            }
            // Box vs Box
            else if (a.Shape == ColliderShape.Box && b.Shape == ColliderShape.Box)
            {
                collisionDetected = FastBoxVsBoxCheck(a, b);
            }
            // Circle vs Box
            else if (a.Shape == ColliderShape.Circle && b.Shape == ColliderShape.Box)
            {
                collisionDetected = FastCircleVsBoxCheck(a, b);
            }
            // Box vs Circle
            else
            {
                collisionDetected = FastCircleVsBoxCheck(b, a);
            }

            // Only get CollisionInfo if collision was detected
            if (collisionDetected)
            {
                info = GetCollisionInfo();
                info.Other = b;

                // Now do full collision detection with detailed info
                if (a.Shape == ColliderShape.Circle && b.Shape == ColliderShape.Circle)
                {
                    return CircleVsCircle(a, b, info);
                }
                else if (a.Shape == ColliderShape.Box && b.Shape == ColliderShape.Box)
                {
                    return BoxVsBox(a, b, info);
                }
                else if (a.Shape == ColliderShape.Circle && b.Shape == ColliderShape.Box)
                {
                    return CircleVsBox(a, b, info);
                }
                else
                {
                    bool result = CircleVsBox(b, a, info);
                    if (result)
                    {
                        info.Normal = -info.Normal;
                        info.Other = b;
                    }
                    return result;
                }
            }
            else
            {
                info = null;
                return false;
            }
        }

        /// <summary>
        /// Physics2D-based collision detection using Unity's collision system
        /// </summary>
        private bool CheckCollisionPhysics2D(ICollidable a, ICollidable b, out CollisionInfo info)
        {
            info = null;

            // Ensure Physics2D layer mapping is available
            if (physics2DLayerMapping == null)
            {
                Debug.LogWarning("Physics2DLayerMapping not assigned! Falling back to custom collision detection.");
                return CheckCollisionCustom(a, b, out info);
            }

            // Convert ICollidable to SpatialCollider for Physics2D integration
            SpatialCollider spatialA = a as SpatialCollider;
            SpatialCollider spatialB = b as SpatialCollider;

            if (spatialA == null || spatialB == null)
            {
                // Fallback to custom collision if not SpatialCollider
                return CheckCollisionCustom(a, b, out info);
            }

            // Use Physics2D utilities for collision detection
            bool collisionDetected = Physics2DCollisionUtilities.CheckCollision(spatialA, spatialB, physics2DLayerMapping);

            if (collisionDetected)
            {
                // Create CollisionInfo for Physics2D collision
                info = GetCollisionInfo();
                info.Other = b;

                // Calculate collision details using geometric methods for now
                // TODO: Extract collision details from Physics2D results in future optimization
                Vector2 diff = b.Position - a.Position;
                float distance = diff.magnitude;

                if (distance > 0)
                {
                    info.Normal = diff.normalized;
                }
                else
                {
                    info.Normal = Vector2.right; // Default normal
                }

                // Approximate penetration based on shapes
                if (a.Shape == ColliderShape.Circle && b.Shape == ColliderShape.Circle)
                {
                    info.Penetration = (a.Radius + b.Radius) - distance;
                }
                else
                {
                    info.Penetration = 0.1f; // Default small penetration for box collisions
                }

                info.ContactPoint = a.Position + info.Normal * a.Radius;
                return true;
            }

            return false;
        }
        
        private bool CircleVsCircle(ICollidable a, ICollidable b, CollisionInfo info)
        {
            // Use cached Vector2 to avoid allocations
            _cachedDiff.x = b.Position.x - a.Position.x;
            _cachedDiff.y = b.Position.y - a.Position.y;
            float distSq = _cachedDiff.x * _cachedDiff.x + _cachedDiff.y * _cachedDiff.y;
            float radiusSum = a.Radius + b.Radius;
            
            if (distSq < radiusSum * radiusSum)
            {
                float dist = Mathf.Sqrt(distSq);
                if (dist > 0)
                {
                    _cachedNormal.x = _cachedDiff.x / dist;
                    _cachedNormal.y = _cachedDiff.y / dist;
                }
                else
                {
                    _cachedNormal.x = 1f;
                    _cachedNormal.y = 0f;
                }
                
                info.Normal = _cachedNormal;
                info.Penetration = radiusSum - dist;
                _cachedContactPoint.x = a.Position.x + _cachedNormal.x * a.Radius;
                _cachedContactPoint.y = a.Position.y + _cachedNormal.y * a.Radius;
                info.ContactPoint = _cachedContactPoint;
                return true;
            }
            
            return false;
        }
        
        private bool BoxVsBox(ICollidable a, ICollidable b, CollisionInfo info)
        {
            // Use cached Vector2 instances to avoid allocations
            float aHalfSizeX = a.Size.x * 0.5f;
            float aHalfSizeY = a.Size.y * 0.5f;
            float bHalfSizeX = b.Size.x * 0.5f;
            float bHalfSizeY = b.Size.y * 0.5f;
            
            float aMinX = a.Position.x - aHalfSizeX;
            float aMaxX = a.Position.x + aHalfSizeX;
            float aMinY = a.Position.y - aHalfSizeY;
            float aMaxY = a.Position.y + aHalfSizeY;
            
            float bMinX = b.Position.x - bHalfSizeX;
            float bMaxX = b.Position.x + bHalfSizeX;
            float bMinY = b.Position.y - bHalfSizeY;
            float bMaxY = b.Position.y + bHalfSizeY;
            
            if (aMinX < bMaxX && aMaxX > bMinX &&
                aMinY < bMaxY && aMaxY > bMinY)
            {
                // Calculate overlap on each axis
                float overlapX = Mathf.Min(aMaxX - bMinX, bMaxX - aMinX);
                float overlapY = Mathf.Min(aMaxY - bMinY, bMaxY - aMinY);
                
                // Find the axis of least penetration
                if (overlapX < overlapY)
                {
                    info.Penetration = overlapX;
                    if (a.Position.x < b.Position.x)
                    {
                        _cachedNormal.x = -1f;
                        _cachedNormal.y = 0f;
                        _cachedContactPoint.x = aMaxX;
                    }
                    else
                    {
                        _cachedNormal.x = 1f;
                        _cachedNormal.y = 0f;
                        _cachedContactPoint.x = aMinX;
                    }
                    _cachedContactPoint.y = Mathf.Clamp(b.Position.y, aMinY, aMaxY);
                }
                else
                {
                    info.Penetration = overlapY;
                    if (a.Position.y < b.Position.y)
                    {
                        _cachedNormal.x = 0f;
                        _cachedNormal.y = -1f;
                        _cachedContactPoint.y = aMaxY;
                    }
                    else
                    {
                        _cachedNormal.x = 0f;
                        _cachedNormal.y = 1f;
                        _cachedContactPoint.y = aMinY;
                    }
                    _cachedContactPoint.x = Mathf.Clamp(b.Position.x, aMinX, aMaxX);
                }
                
                info.Normal = _cachedNormal;
                info.ContactPoint = _cachedContactPoint;
                return true;
            }
            
            return false;
        }
        
        private bool CircleVsBox(ICollidable circle, ICollidable box, CollisionInfo info)
        {
            // Use cached Vector2 instances to avoid allocations
            float boxHalfSizeX = box.Size.x * 0.5f;
            float boxHalfSizeY = box.Size.y * 0.5f;
            
            float boxMinX = box.Position.x - boxHalfSizeX;
            float boxMaxX = box.Position.x + boxHalfSizeX;
            float boxMinY = box.Position.y - boxHalfSizeY;
            float boxMaxY = box.Position.y + boxHalfSizeY;
            
            // Find closest point on box to circle center
            _cachedClosest.x = Mathf.Clamp(circle.Position.x, boxMinX, boxMaxX);
            _cachedClosest.y = Mathf.Clamp(circle.Position.y, boxMinY, boxMaxY);
            
            _cachedDiff.x = circle.Position.x - _cachedClosest.x;
            _cachedDiff.y = circle.Position.y - _cachedClosest.y;
            float distSq = _cachedDiff.x * _cachedDiff.x + _cachedDiff.y * _cachedDiff.y;
            
            if (distSq < circle.Radius * circle.Radius)
            {
                float dist = Mathf.Sqrt(distSq);
                if (dist > 0)
                {
                    _cachedNormal.x = _cachedDiff.x / dist;
                    _cachedNormal.y = _cachedDiff.y / dist;
                }
                else
                {
                    _cachedNormal.x = 0f;
                    _cachedNormal.y = 1f;
                }
                
                info.Normal = _cachedNormal;
                info.Penetration = circle.Radius - dist;
                info.ContactPoint = _cachedClosest;
                return true;
            }
            
            return false;
        }
        
        public CollisionInfo GetCollisionInfo()
        {
            if (_collisionInfoPool.Count > 0)
            {
                _poolHits++;
                return _collisionInfoPool.Dequeue();
            }
            
            _poolMisses++;
            
            // Try to expand pool if under maximum capacity
            if (_currentPoolSize < _maxPoolSize)
            {
                int growthAmount = Mathf.Min(500, _maxPoolSize - _currentPoolSize);
                
                for (int i = 0; i < growthAmount; i++)
                {
                    _collisionInfoPool.Enqueue(new CollisionInfo());
                    _currentPoolSize++;
                }
                
                
                return _collisionInfoPool.Dequeue();
            }
            
            // Pool is at maximum capacity and empty - this indicates a serious design issue
            Debug.LogError($"CollisionManager: Pool exhausted! {_maxPoolSize} CollisionInfo objects in use simultaneously. " +
                         "This indicates either pool misconfiguration or object leak. Increasing pool size temporarily.");
            
            // Emergency expansion beyond normal limits to prevent system crash
            int emergencyExpansion = 1000;
            _maxPoolSize += emergencyExpansion;
            _emergencyPoolExpansions++;
            
            for (int i = 0; i < emergencyExpansion; i++)
            {
                _collisionInfoPool.Enqueue(new CollisionInfo());
                _currentPoolSize++;
            }
            
            Debug.LogWarning($"CollisionManager: Emergency pool expansion by {emergencyExpansion}, new max size: {_maxPoolSize}");
            
            return _collisionInfoPool.Dequeue();
        }
        
        public void ReturnCollisionInfo(CollisionInfo info)
        {
            if (info == null) return;
            
            info.Reset();
            _collisionInfoPool.Enqueue(info);
        }
        

        
        /// <summary>
        /// Validate system configuration and component dependencies
        /// </summary>
        private void ValidateSystemConfiguration()
        {
            // Validate TilemapChunkManager dependency
            if (chunkManager == null)
            {
                chunkManager = TilemapChunkManager.Instance;
                if (chunkManager == null)
                {
                    Debug.LogError("CollisionManager: TilemapChunkManager not found! Please assign in inspector or ensure TilemapChunkManager.Instance is available.");
                }
            }
            
            // Validate spatial system configuration
            if (chunkManager == null)
            {
                Debug.LogError("CollisionManager: Integrated spatial collision system requires TilemapChunkManager!");
            }
            
            if (chunkCollisions == null)
            {
                Debug.LogError("CollisionManager: Spatial collision system failed to initialize!");
            }
            
        }
        
        /// <summary>
        /// Initialize the collision info pool with the specified size
        /// </summary>
        private void InitializeCollisionInfoPool(int size)
        {
            _maxPoolSize = size;
            _currentPoolSize = size;
            
            for (int i = 0; i < size; i++)
            {
                _collisionInfoPool.Enqueue(new CollisionInfo());
            }
            
        }
        
        // B1: InitializeHashSetPool method removed - chunks manage their own pools
        
        // B1: GetPooledHashSet method removed - chunks manage their own pools
        
        // B1: ReturnHashSetToPool method removed - chunks manage their own pools
        
        // B6: LogHashSetPoolExhaustionDebug method removed - no global pool to debug
        
        // B6: DebugHashSetPoolStatus method removed - no global pool to debug
        
        // B6: DetectHashSetLeaks method removed - no global pool to check for leaks
        
        [Button("Force Process All Pending Registrations", ButtonSizes.Medium)]
        private void ForceProcessAllPendingRegistrations()
        {
            int originalThrottle = maxRegistrationsPerFrame;
            int processed = 0;
            
            
            // Temporarily disable throttling
            maxRegistrationsPerFrame = int.MaxValue;
            
            while (_registrationQueue.Count > 0)
            {
                var collidable = _registrationQueue.Dequeue();
                
                if (collidable != null && !_activeCollidables.Contains(collidable))
                {
                    _pendingRegistrationSet.Remove(collidable);
                    PerformRegistration(collidable);
                    processed++;
                }
                else
                {
                    _pendingRegistrationSet.Remove(collidable);
                }
            }
            
            // Restore original throttling
            maxRegistrationsPerFrame = originalThrottle;
            
        }
        
        [Button("Analyze Startup Performance", ButtonSizes.Medium)]
        private void AnalyzeStartupPerformance()
        {
            if (_isStartupPhase)
            {
                // B6: Pool usage removed - chunks manage their own pools
                
                if (_pendingRegistrations > 100)
                {
                    Debug.LogWarning($"HIGH PENDING REGISTRATIONS: {_pendingRegistrations} objects waiting!");
                    Debug.LogWarning("Consider increasing maxRegistrationsPerFrame or disabling throttling for startup.");
                }
            }
            else
            {
                LogStartupSummary();
            }
        }
        
        [Title("Chunk Processing Mode")]
        [InfoBox("OPTIMIZED SINGLE-CHUNK MODE ACTIVE\n\nThis collision manager processes ONLY the current player chunk with neighbor buffer for smooth transitions.\nAll preset buttons below are disabled in optimized mode.", InfoMessageType.Warning)]
        
        [HorizontalGroup("ChunkPresets")]
        [Button("OPTIMIZED MODE\n(Active)", ButtonSizes.Large)]
        [InfoBox("Currently active: Processes only the current player chunk for maximum performance.", InfoMessageType.Info)]
        private void ShowStrictModeInfo()
        {
        }
        
        [HorizontalGroup("ChunkPresets")]
        [Button("Multi-Chunk\n(Disabled)", ButtonSizes.Large)]
        [InfoBox("Multi-chunk processing is disabled in optimized mode.", InfoMessageType.Warning)]
        private void ShowMultiChunkDisabled()
        {
            Debug.LogWarning($"CollisionManager: Multi-chunk processing is DISABLED in optimized single-chunk mode.");
            Debug.LogWarning($"To enable multi-chunk processing, you need to revert to the original collision system.");
        }
        
        /// <summary>
        /// Monitor memory pressure and GC activity to prevent allocation spikes
        /// </summary>
        private void CheckMemoryPressure()
        {
            if (Time.time - _lastMemoryCheckTime >= MEMORY_CHECK_INTERVAL)
            {
                _lastMemoryCheckTime = Time.time;
                
                // Check current memory usage
                long currentMemory = System.GC.GetTotalMemory(false);
                int currentGcCount = System.GC.CollectionCount(0); // Gen 0 collections
                
                // Calculate memory pressure score (B6: HashSet pool usage removed)
                float collisionInfoUsageRatio = (_maxPoolSize - _collisionInfoPool.Count) / (float)_maxPoolSize;
                float registrationBacklogRatio = _registrationQueue.Count / 100f; // Normalize to reasonable range
                
                _memoryPressureScore = (collisionInfoUsageRatio * 0.6f) + (registrationBacklogRatio * 0.4f);
                _memoryPressureScore = Mathf.Clamp01(_memoryPressureScore);
                
                // Check for new GC collections
                if (currentGcCount > _gcCollectionCount)
                {
                    long memoryDelta = currentMemory - _lastGcMemory;
                    if (memoryDelta > 1024 * 1024) // More than 1MB allocated since last check
                    {
                        Debug.LogWarning($"CollisionManager: Significant memory allocation detected: {memoryDelta / 1024 / 1024}MB. " +
                                       $"GC collections: {currentGcCount - _gcCollectionCount}. Memory pressure: {_memoryPressureScore:F2}");
                    }
                    _gcCollectionCount = currentGcCount;
                }
                
                // High pressure warnings
                if (_memoryPressureScore > HIGH_PRESSURE_THRESHOLD)
                {
                    Debug.LogWarning($"CollisionManager: HIGH MEMORY PRESSURE detected! Score: {_memoryPressureScore:F2}");
                    // B6: HashSet pool usage warning removed - chunks manage their own pools
                    Debug.LogWarning($"  - CollisionInfo pool usage: {(_maxPoolSize - _collisionInfoPool.Count)}/{_maxPoolSize} ({collisionInfoUsageRatio:P})");
                    Debug.LogWarning($"  - Registration backlog: {_registrationQueue.Count}");
                    Debug.LogWarning($"  - Emergency expansions: {_emergencyPoolExpansions}");
                    
                    // B6: HashSet pool remedial actions removed
                    if (collisionInfoUsageRatio > 0.9f)
                    {
                        Debug.LogWarning("RECOMMENDATION: Increase CollisionInfo pool size or reduce collision complexity");
                    }
                    if (_registrationQueue.Count > 50)
                    {
                        Debug.LogWarning("RECOMMENDATION: Increase maxRegistrationsPerFrame or disable throttling temporarily");
                    }
                }
                
                _lastGcMemory = currentMemory;
            }
        }
        
        // B6: CheckHashSetPoolUsage method removed - no global pool to monitor
        
        /// <summary>
        /// Setup reverse collision info without creating new objects
        /// </summary>
        public void SetupReverseCollisionInfo(CollisionInfo info, ICollidable newOther)
        {
            var originalNormal = info.Normal;
            
            info.Other = newOther;
            // Reuse cached vector to avoid allocation
            _cachedNormal.x = -originalNormal.x;
            _cachedNormal.y = -originalNormal.y;
            info.Normal = _cachedNormal;
            // ContactPoint and Penetration remain the same for reverse collision
        }
        
        // Zero-GC helper methods for debug logging (replacements for LINQ)
        
        /// <summary>
        /// Find top N entries from a dictionary by value without LINQ allocations
        /// </summary>
        private void GetTopEntriesByValue<T>(Dictionary<T, int> dict, List<KeyValuePair<T, int>> results, int count)
        {
            results.Clear();
            
            foreach (var kvp in dict)
            {
                if (results.Count < count)
                {
                    results.Add(kvp);
                    // Simple insertion sort for small count
                    for (int i = results.Count - 1; i > 0; i--)
                    {
                        if (results[i].Value > results[i - 1].Value)
                        {
                            var temp = results[i];
                            results[i] = results[i - 1];
                            results[i - 1] = temp;
                        }
                        else break;
                    }
                }
                else if (kvp.Value > results[count - 1].Value)
                {
                    results[count - 1] = kvp;
                    // Bubble up the new entry
                    for (int i = count - 1; i > 0; i--)
                    {
                        if (results[i].Value > results[i - 1].Value)
                        {
                            var temp = results[i];
                            results[i] = results[i - 1];
                            results[i - 1] = temp;
                        }
                        else break;
                    }
                }
            }
        }
        
        /// <summary>
        /// Find maximum value entry in a dictionary without LINQ allocations
        /// </summary>
        private KeyValuePair<T, int> GetMaxValueEntry<T>(Dictionary<T, int> dict)
        {
            var maxEntry = new KeyValuePair<T, int>();
            bool hasEntry = false;
            
            foreach (var kvp in dict)
            {
                if (!hasEntry || kvp.Value > maxEntry.Value)
                {
                    maxEntry = kvp;
                    hasEntry = true;
                }
            }
            
            return maxEntry;
        }
        
        /// <summary>
        /// Build a string from key-value pairs without LINQ allocations
        /// </summary>
        private string BuildTypesString(List<KeyValuePair<string, int>> types)
        {
            if (types.Count == 0) return "None";
            
            var sb = new System.Text.StringBuilder();
            for (int i = 0; i < types.Count; i++)
            {
                if (i > 0) sb.Append(", ");
                sb.Append(types[i].Key).Append("(").Append(types[i].Value).Append(")");
            }
            return sb.ToString();
        }
        
        // Cached lists for zero-GC debug operations
        private readonly List<KeyValuePair<string, int>> _debugTopTypes = new List<KeyValuePair<string, int>>(10);
        private readonly List<KeyValuePair<string, int>> _debugAllTypes = new List<KeyValuePair<string, int>>(50);

        private void LogChunkModeChange()
        {
            // Debug logging removed - method kept for compatibility
        }
        
        /// <summary>
        /// Force optimized single-chunk mode regardless of configuration
        /// </summary>
        private void SynchronizePresetWithChunkRange()
        {
            // Force optimized single-chunk mode
            chunkPreset = ChunkProcessingPreset.SingleChunk;
            neighbourChunkRange = 0; // Ensure this is 0 even if set differently
            
        }

#if UNITY_EDITOR
        // B6: HashSet usage warning helper methods removed - no global pool to monitor
        
        private string GetChunkPerformanceInfo()
        {
            return $"OPTIMIZED MODE: Processing exactly 1 chunk (OPTIMAL performance)";
        }
        
        private InfoMessageType GetChunkPerformanceInfoType()
        {
            return InfoMessageType.Info; // Always optimal in optimized mode
        }
        
        private void OnDrawGizmos()
        {
            if (!Application.isPlaying) return;

            // Draw the bounds of the OPTIMIZED SINGLE-CHUNK being processed
            // OPTIMIZED MODE: Only visualizes the current player chunk (no neighbors)
            Gizmos.color = Color.green; // Green indicates optimized mode
            Vector2 size = _currentChunkMax - _currentChunkMin;
            Vector2 center = _currentChunkMin + size / 2f;

            if (size != Vector2.zero)
            {
                // Draw solid cube for current chunk
                Gizmos.DrawWireCube(center, size);
                
                // Draw filled cube to emphasize optimized single-chunk processing
                Gizmos.color = new Color(0, 1, 0, 0.1f); // Transparent green
                Gizmos.DrawCube(center, size);
                
                // Draw text label if possible
                Gizmos.color = Color.white;
            }
        }
#endif
        
        /// <summary>
        /// Cleanup all spatial collision data and pooled objects when component is destroyed
        /// </summary>
        private void OnDestroy()
        {
            if (chunkCollisions != null)
            {
                // Clear all chunk data and return pooled objects
                foreach (var chunkData in chunkCollisions.Values)
                {
                    chunkData?.Clear();
                }
                chunkCollisions.Clear();
            }
            
            // Clear tracking collections
            activeChunks?.Clear();
            objectToChunk?.Clear();
            
            // Clear reusable buffers
            chunkObjectsBuffer?.Clear();
            nearbyObjectsBuffer?.Clear();
            neighborChunksBuffer?.Clear();

        }

        /// <summary>
        /// Ensure Physics2D components are properly set up for a collidable object
        /// </summary>
        private void EnsurePhysics2DComponents(ICollidable collidable)
        {
            // Only process SpatialCollider objects for Physics2D integration
            if (collidable is SpatialCollider spatialCollider)
            {
                // Use the auto-manager to ensure proper Collider2D setup
                Collider2DAutoManager.EnsureCollider2D(spatialCollider, physics2DLayerMapping);
            }
        }

        /// <summary>
        /// Initialize Physics2D collision system
        /// </summary>
        private void InitializePhysics2DSystem()
        {
            if (physics2DLayerMapping == null)
            {
                Debug.LogWarning("Physics2DLayerMapping not assigned! Physics2D collision detection will fall back to custom detection.");
                return;
            }

            // Validate layer mappings
            physics2DLayerMapping.ValidateLayerMappings();

            // Clear any existing caches
            Physics2DCollisionUtilities.ClearCaches();

            Debug.Log("Physics2D collision system initialized successfully.");
        }

        /// <summary>
        /// Get the current collision detection mode
        /// </summary>
        public CollisionDetectionMode GetCollisionMode()
        {
            return collisionMode;
        }

        /// <summary>
        /// Get the Physics2D layer mapping
        /// </summary>
        public Physics2DLayerMapping GetPhysics2DLayerMapping()
        {
            return physics2DLayerMapping;
        }

        /// <summary>
        /// Validate zero-GC compliance for Physics2D collision detection
        /// </summary>
        [Button("Validate Zero-GC Compliance")]
        private void ValidateZeroGCCompliance()
        {
            if (collisionMode != CollisionDetectionMode.Physics2D)
            {
                Debug.Log("Zero-GC validation only applies to Physics2D mode.");
                return;
            }

            var stats = Physics2DCollisionUtilities.GetStats();
            Debug.Log($"Physics2D Collision Utilities Stats:\n" +
                     $"Contact Filter Cache Size: {stats.ContactFilterCacheSize}\n" +
                     $"Overlap Results Array Size: {stats.OverlapResultsArraySize}\n" +
                     $"Cast Results Array Size: {stats.CastResultsArraySize}");

            // Check if layer mapping is properly configured
            if (physics2DLayerMapping == null)
            {
                Debug.LogError("Physics2DLayerMapping is not assigned! This will cause fallbacks to custom collision detection.");
            }
            else
            {
                Debug.Log("Physics2D layer mapping is properly configured.");
            }
        }
    }
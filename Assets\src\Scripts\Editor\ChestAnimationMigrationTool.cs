using UnityEngine;
using UnityEditor;
using System.Linq;

    public class ChestAnimationMigrationTool : EditorWindow
    {
        private bool showFoundChests = false;
        private ChestComponent[] foundChests = new ChestComponent[0];
        private SpriteAnimation defaultClosedAnimation;
        private SpriteAnimation defaultOpeningAnimation;
        private SpriteAnimation defaultOpenedAnimation;
        
        [MenuItem("2D Rogue/Utilities/Chest Animation Migration Tool")]
        public static void ShowWindow()
        {
            var window = GetWindow<ChestAnimationMigrationTool>("Chest Animation Migration");
            window.minSize = new Vector2(450, 400);
            window.Show();
        }
        
        private void OnGUI()
        {
            EditorGUILayout.Space(10);
            GUILayout.Label("Chest Animation Migration Tool", EditorStyles.largeLabel);
            
            EditorGUILayout.HelpBox(
                "This tool migrates ChestComponents from the old string-based animation system to the new SpriteAnimation reference system.",
                MessageType.Info);
            
            EditorGUILayout.Space(10);
            
            // Default animations
            GUILayout.Label("Default Animations", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("These animations will be assigned to chests that don't have specific animations.", MessageType.None);
            
            defaultClosedAnimation = (SpriteAnimation)EditorGUILayout.ObjectField("Default Closed Animation", defaultClosedAnimation, typeof(SpriteAnimation), false);
            defaultOpeningAnimation = (SpriteAnimation)EditorGUILayout.ObjectField("Default Opening Animation", defaultOpeningAnimation, typeof(SpriteAnimation), false);
            defaultOpenedAnimation = (SpriteAnimation)EditorGUILayout.ObjectField("Default Opened Animation", defaultOpenedAnimation, typeof(SpriteAnimation), false);
            
            EditorGUILayout.Space(5);
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Auto-Find Chest Animations"))
            {
                AutoFindDefaultAnimations();
            }
            if (GUILayout.Button("Clear All"))
            {
                defaultClosedAnimation = null;
                defaultOpeningAnimation = null;
                defaultOpenedAnimation = null;
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space(15);
            
            // Scan for chests
            GUILayout.Label("Migration Process", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Scan for ChestComponents", GUILayout.Height(25)))
            {
                ScanForChests();
            }
            
            if (foundChests.Length > 0)
            {
                showFoundChests = EditorGUILayout.Toggle("Show Details", showFoundChests, GUILayout.Width(100));
            }
            EditorGUILayout.EndHorizontal();
            
            if (foundChests.Length > 0)
            {
                EditorGUILayout.Space(5);
                EditorGUILayout.LabelField($"Found {foundChests.Length} ChestComponent(s)");
                
                if (showFoundChests)
                {
                    DrawFoundChests();
                }
                
                EditorGUILayout.Space(10);
                
                // Migration buttons
                bool canMigrate = defaultClosedAnimation != null || defaultOpeningAnimation != null || defaultOpenedAnimation != null;
                
                if (!canMigrate)
                {
                    EditorGUILayout.HelpBox("Please assign at least one default animation before migrating.", MessageType.Warning);
                }
                
                EditorGUI.BeginDisabledGroup(!canMigrate);
                
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Migrate All Chests", GUILayout.Height(30)))
                {
                    MigrateAllChests();
                }
                
                if (GUILayout.Button("Migrate Scene Chests Only", GUILayout.Height(30)))
                {
                    MigrateSceneChests();
                }
                EditorGUILayout.EndHorizontal();
                
                EditorGUI.EndDisabledGroup();
            }
            else
            {
                EditorGUILayout.Space(10);
                EditorGUILayout.HelpBox("Click 'Scan for ChestComponents' to find chests in your project.", MessageType.None);
            }
        }
        
        private void AutoFindDefaultAnimations()
        {
            string[] guids = AssetDatabase.FindAssets("t:SpriteAnimation");
            
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                SpriteAnimation animation = AssetDatabase.LoadAssetAtPath<SpriteAnimation>(path);
                
                if (animation != null)
                {
                    string animName = animation.AnimationName.ToLower();
                    
                    if (animName.Contains("chest"))
                    {
                        if (animName.Contains("closed") && defaultClosedAnimation == null)
                            defaultClosedAnimation = animation;
                        else if (animName.Contains("opening") && defaultOpeningAnimation == null)
                            defaultOpeningAnimation = animation;
                        else if (animName.Contains("opened") && defaultOpenedAnimation == null)
                            defaultOpenedAnimation = animation;
                    }
                }
            }
            
            if (defaultClosedAnimation != null || defaultOpeningAnimation != null || defaultOpenedAnimation != null)
            {
                Debug.Log("Auto-found chest animations!");
            }
            else
            {
                EditorUtility.DisplayDialog("No Animations Found", 
                    "No chest animations found in the project. Please create them using the Sprite Animation Tool.", "OK");
            }
        }
        
        private void ScanForChests()
        {
            // Find all ChestComponents in the project (including prefabs)
            string[] prefabGuids = AssetDatabase.FindAssets("t:Prefab");
            var chestList = new System.Collections.Generic.List<ChestComponent>();
            
            // Scan prefabs
            foreach (string guid in prefabGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                
                if (prefab != null)
                {
                    var chestComponent = prefab.GetComponent<ChestComponent>();
                    if (chestComponent != null)
                    {
                        chestList.Add(chestComponent);
                    }
                }
            }
            
            // Scan scene objects
            var sceneChests = FindObjectsOfType<ChestComponent>();
            chestList.AddRange(sceneChests);
            
            foundChests = chestList.ToArray();
            
            Debug.Log($"Found {foundChests.Length} ChestComponent(s) in the project");
        }
        
        private void DrawFoundChests()
        {
            EditorGUILayout.Space(5);
            
            var scrollPosition = Vector2.zero;
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(150));
            
            foreach (var chest in foundChests)
            {
                EditorGUILayout.BeginHorizontal();
                
                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.ObjectField(chest, typeof(ChestComponent), true);
                EditorGUI.EndDisabledGroup();
                
                string status = GetChestMigrationStatus(chest);
                GUIStyle statusStyle = new GUIStyle(EditorStyles.label);
                statusStyle.normal.textColor = status == "Migrated" ? Color.green : Color.orange;
                
                EditorGUILayout.LabelField(status, statusStyle, GUILayout.Width(80));
                
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndScrollView();
        }
        
        private string GetChestMigrationStatus(ChestComponent chest)
        {
            // Check if chest has new animation references
            var closedAnim = GetPrivateField(chest, "closedAnimation") as SpriteAnimation;
            var openingAnim = GetPrivateField(chest, "openingAnimation") as SpriteAnimation;
            var openedAnim = GetPrivateField(chest, "openedAnimation") as SpriteAnimation;
            
            if (closedAnim != null || openingAnim != null || openedAnim != null)
            {
                return "Migrated";
            }
            
            return "Needs Migration";
        }
        
        private void MigrateAllChests()
        {
            if (!EditorUtility.DisplayDialog("Migrate All Chests", 
                $"This will migrate {foundChests.Length} ChestComponent(s) to use SpriteAnimation references.\n\n" +
                "This action cannot be undone. Continue?", "Migrate", "Cancel"))
            {
                return;
            }
            
            int migratedCount = 0;
            
            foreach (var chest in foundChests)
            {
                if (MigrateChest(chest))
                {
                    migratedCount++;
                }
            }
            
            AssetDatabase.SaveAssets();
            EditorUtility.SetDirty(this);
            
            EditorUtility.DisplayDialog("Migration Complete", 
                $"Successfully migrated {migratedCount} out of {foundChests.Length} ChestComponent(s).", "OK");
            
            Debug.Log($"Chest migration complete: {migratedCount}/{foundChests.Length} migrated");
        }
        
        private void MigrateSceneChests()
        {
            var sceneChests = foundChests.Where(c => c.gameObject.scene.IsValid()).ToArray();
            
            if (sceneChests.Length == 0)
            {
                EditorUtility.DisplayDialog("No Scene Chests", "No ChestComponents found in the current scene.", "OK");
                return;
            }
            
            if (!EditorUtility.DisplayDialog("Migrate Scene Chests", 
                $"This will migrate {sceneChests.Length} ChestComponent(s) in the current scene.\n\n" +
                "This action cannot be undone. Continue?", "Migrate", "Cancel"))
            {
                return;
            }
            
            int migratedCount = 0;
            
            foreach (var chest in sceneChests)
            {
                if (MigrateChest(chest))
                {
                    migratedCount++;
                }
            }
            
            EditorUtility.DisplayDialog("Migration Complete", 
                $"Successfully migrated {migratedCount} out of {sceneChests.Length} scene ChestComponent(s).", "OK");
            
            Debug.Log($"Scene chest migration complete: {migratedCount}/{sceneChests.Length} migrated");
        }
        
        private bool MigrateChest(ChestComponent chest)
        {
            try
            {
                // Set new animation references
                SetPrivateField(chest, "closedAnimation", defaultClosedAnimation);
                SetPrivateField(chest, "openingAnimation", defaultOpeningAnimation);
                SetPrivateField(chest, "openedAnimation", defaultOpenedAnimation);
                
                // Mark as dirty for saving
                EditorUtility.SetDirty(chest);
                
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to migrate chest '{chest.name}': {e.Message}");
                return false;
            }
        }
        
        private object GetPrivateField(object target, string fieldName)
        {
            var field = target.GetType().GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return field?.GetValue(target);
        }
        
        private void SetPrivateField(object target, string fieldName, object value)
        {
            var field = target.GetType().GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                field.SetValue(target, value);
            }
            else
            {
                Debug.LogWarning($"Field '{fieldName}' not found in type '{target.GetType().Name}'");
            }
        }
    }

using UnityEngine;
using Sirenix.OdinInspector;
using PrimeTween;

/// <summary>
/// Currency pickup component with magnetic attraction to player.
/// Integrates with collision system and object pooling for performance.
/// </summary>
[RequireComponent(typeof(SpatialCollider))]
public class CurrencyPickup : MonoBehaviour, ISpawnable, ISpatialCollisionHandler
{
    [Title("Pickup Configuration")]
    [SerializeField, Range(1f, 10f)]
    [Tooltip("Range at which the currency will start moving toward the player")]
    private float attractionRange = 3f;
    
    [SerializeField, Range(1f, 15f)]
    [Tooltip("Speed of movement toward the player")]
    private float moveSpeed = 8f;
    
    [SerializeField, Range(1, 100)]
    [Tooltip("Currency value of this pickup")]
    private int currencyValue = 1;
    
    [Title("Animation Settings")]
    [SerializeField, Range(0.1f, 2f)]
    [Tooltip("Duration of the pickup animation")]
    private float pickupAnimationDuration = 0.3f;
    
    [Serial<PERSON><PERSON><PERSON>, Range(0.5f, 2f)]
    [Tooltip("Scale multiplier during pickup animation")]
    private float pickupScaleMultiplier = 1.2f;
    
    [SerializeField, Range(0.1f, 1f)]
    [Tooltip("Duration of the spawn scale-up animation")]
    private float spawnAnimationDuration = 0.25f;
    
    [SerializeField]
    [Tooltip("Ease type for spawn animation")]
    private Ease spawnAnimationEase = Ease.OutBack;
    
    [Title("Debug Settings")]
    [SerializeField]
    private bool enableDebugLogging = false;
    
    [SerializeField]
    private bool showDebugGizmos = false;
    
    // Cached Components
    private SpatialCollider spatialCollider;
    private Transform cachedTransform;
    private Vector3 originalScale;
    
    // State Management
    private bool isBeingAttracted = false;
    private bool isBeingCollected = false;
    private Transform playerTransform;
    private float attractionStartTime;
    private Vector3 attractionStartPosition;
    
    // Animation
    private Sequence pickupTween;
    private Tween spawnTween;
    
    #region Unity Lifecycle
    
    private void Awake()
    {
        // Cache components
        spatialCollider = GetComponent<SpatialCollider>();
        cachedTransform = transform;
        originalScale = cachedTransform.localScale;
        
        // Configure spatial collider
        if (spatialCollider != null)
        {
            spatialCollider.SetLayer(CollisionLayers.Pickup);
            spatialCollider.SetTrigger(true);
        }
        else
        {
            Debug.LogError($"[CurrencyPickup] SpatialCollider component is missing on {gameObject.name}");
        }
    }
    
    private void Start()
    {
        // Cache player transform reference
        playerTransform = PlayerManager.PlayerTransform;
        
        if (playerTransform == null)
        {
            Debug.LogError("[CurrencyPickup] PlayerManager.PlayerTransform is null!");
        }
    }
    
    private void Update()
    {
        if (isBeingCollected || playerTransform == null)
            return;
            
        Vector3 targetPosition = PlayerManager.GetPrimaryTargetPosition();
        float distanceToPlayer = Vector3.Distance(cachedTransform.position, targetPosition);
        
        if (!isBeingAttracted && distanceToPlayer <= attractionRange)
        {
            StartAttractionToPlayer();
        }
        
        // Handle dynamic player tracking during attraction
        if (isBeingAttracted)
        {
            MoveTowardsPlayer(targetPosition, distanceToPlayer);
        }
    }
    
    private void OnDestroy()
    {
        // Clean up tweens
        StopAllTweens();
    }
    
    #endregion
    
    #region ISpawnable Implementation
    
    public void OnSpawn()
    {
        // Reset state
        isBeingAttracted = false;
        isBeingCollected = false;
        
        // Start with zero scale for spawn animation
        cachedTransform.localScale = Vector3.zero;
        
        // Ensure spatial collider is enabled
        if (spatialCollider != null)
        {
            spatialCollider.enabled = true;
        }
        
        // Cache player transform if not already cached
        if (playerTransform == null)
        {
            playerTransform = PlayerManager.PlayerTransform;
        }
        
        // Play spawn scale-up animation
        PlaySpawnAnimation();
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CurrencyPickup] Spawned with value: {currencyValue}");
        }
    }
    
    public void OnDespawn()
    {
        // Stop all animations
        StopAllTweens();
        
        // Reset state
        isBeingAttracted = false;
        isBeingCollected = false;
        cachedTransform.localScale = originalScale;
        
        if (enableDebugLogging)
        {
            Debug.Log("[CurrencyPickup] Despawned");
        }
    }
    
    #endregion
    
    #region ISpatialCollisionHandler Implementation
    
    public void HandleCollisionEnter(CollisionInfo collision)
    {
        // Not used for trigger-based pickup
    }
    
    public void HandleCollisionStay(CollisionInfo collision)
    {
        // Not used for trigger-based pickup
    }
    
    public void HandleCollisionExit(CollisionInfo collision)
    {
        // Not used for trigger-based pickup
    }
    
    public void HandleTriggerEnter(CollisionInfo collision)
    {
        if (isBeingCollected)
            return;
            
        if (collision.Other.Layer.HasFlag(CollisionLayers.Player))
        {
            CollectCurrency();
        }
    }
    
    public void HandleTriggerStay(CollisionInfo collision)
    {
        // Not needed for currency pickup
    }
    
    public void HandleTriggerExit(CollisionInfo collision)
    {
        // Not needed for currency pickup
    }
    
    #endregion
    
    #region Attraction and Collection
    
    private void StartAttractionToPlayer()
    {
        if (isBeingAttracted || isBeingCollected || playerTransform == null)
            return;
            
        isBeingAttracted = true;
        attractionStartTime = Time.time;
        attractionStartPosition = cachedTransform.position;
        
        if (enableDebugLogging)
        {
            Debug.Log("[CurrencyPickup] Starting attraction to player");
        }
    }
    
    /// <summary>
    /// Handles dynamic movement towards the player's current position with smooth InBack easing
    /// </summary>
    private void MoveTowardsPlayer(Vector3 targetPosition, float distanceToPlayer)
    {
        // Calculate time-based progress for the easing
        float elapsedTime = Time.time - attractionStartTime;
        float estimatedDuration = Vector3.Distance(attractionStartPosition, targetPosition) / moveSpeed;
        float progress = Mathf.Clamp01(elapsedTime / estimatedDuration);
        
        // Get current player position for dynamic tracking
        Vector3 currentTarget = PlayerManager.GetPrimaryTargetPosition();
        
        // Ease.InBack implementation - continuous curve from start to finish
        // c1 = 1.70158, c3 = c1 + 1 = 2.70158
        const float c1 = 1.70158f;
        const float c3 = 2.70158f;
        
        // InBack formula: c3 * t^3 - c1 * t^2
        float easedProgress = c3 * progress * progress * progress - c1 * progress * progress;
        
        // Handle the back effect properly
        Vector3 directionToTarget = (currentTarget - attractionStartPosition).normalized;
        
        if (easedProgress < 0)
        {
            // "Back" phase: Move away from target - SUPER SATISFYING VERSION!
            float backIntensity = Mathf.Abs(easedProgress) * 2.0f; // Dramatic back effect (was 0.5f)
            
            // Add slight perpendicular curve for organic feel (like real physics)
            Vector3 perpendicular = new Vector3(-directionToTarget.y, directionToTarget.x, 0);
            float curve = Mathf.Sin(progress * Mathf.PI * 2f) * backIntensity * 0.15f; // Gentle arc during back
            
            Vector3 backPosition = attractionStartPosition - directionToTarget * backIntensity + perpendicular * curve;
            cachedTransform.position = backPosition;
        }
        else
        {
            // "Forward" phase: Smooth movement towards target with satisfying acceleration
            // Add extra smoothing for the transition from back to forward
            float smoothedProgress = easedProgress * easedProgress * (3f - 2f * easedProgress); // Smoothstep for extra satisfaction
            cachedTransform.position = Vector3.Lerp(attractionStartPosition, currentTarget, smoothedProgress);
        }
        
        // Check if we're close enough to collect (safety fallback)
        if (distanceToPlayer < 0.5f && !isBeingCollected)
        {
            CollectCurrency();
        }
    }
    
    private void CollectCurrency()
    {
        if (isBeingCollected)
            return;
            
        isBeingCollected = true;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CurrencyPickup] Collecting currency: {currencyValue}");
        }
        
        // Add currency to manager
        CurrencyManager.AddCurrency(currencyValue);
        
        // Play pickup animation
        PlayPickupAnimation();
    }
    
    private void PlayPickupAnimation()
    {
        // Scale up briefly then disappear
        var sequence = Sequence.Create(useUnscaledTime: false);
        
        sequence
            .Group(Tween.Scale(
                cachedTransform,
                originalScale * pickupScaleMultiplier,
                pickupAnimationDuration * 0.3f,
                Ease.OutBack
            ))
            .Chain(Tween.Scale(
                cachedTransform,
                Vector3.zero,
                pickupAnimationDuration * 0.7f,
                Ease.InBack
            ))
            .OnComplete(() => {
                // Return to pool
                PoolManager.Instance.Despawn(gameObject);
            });
            
        pickupTween = sequence;
    }
    
    private void StopAllTweens()
    {
        pickupTween.Stop();
        spawnTween.Stop();
    }
    
    /// <summary>
    /// Play spawn scale-up animation from zero to original scale
    /// </summary>
    private void PlaySpawnAnimation()
    {
        // Scale from zero to original scale with satisfying bounce effect
        spawnTween = Tween.Scale(
            cachedTransform,
            originalScale,
            spawnAnimationDuration,
            spawnAnimationEase,
            useUnscaledTime: false
        );
        
        if (enableDebugLogging)
        {
            Debug.Log("[CurrencyPickup] Playing spawn scale-up animation");
        }
    }
    
    #endregion
    
    #region Configuration
    
    /// <summary>
    /// Set the currency value for this pickup
    /// </summary>
    /// <param name="value">Currency value (must be positive)</param>
    public void SetCurrencyValue(int value)
    {
        if (value <= 0)
        {
            Debug.LogWarning($"[CurrencyPickup] Attempted to set invalid currency value: {value}");
            return;
        }
        
        currencyValue = value;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CurrencyPickup] Currency value set to: {currencyValue}");
        }
    }
    
    /// <summary>
    /// Configure pickup parameters
    /// </summary>
    /// <param name="range">Attraction range</param>
    /// <param name="speed">Movement speed</param>
    /// <param name="value">Currency value</param>
    public void ConfigurePickup(float range, float speed, int value)
    {
        attractionRange = Mathf.Clamp(range, 1f, 10f);
        moveSpeed = Mathf.Clamp(speed, 1f, 15f);
        SetCurrencyValue(value);
    }
    
    #endregion
    
    #region Debug Visualization
    
    private void OnDrawGizmos()
    {
        if (!showDebugGizmos)
            return;
            
        // Draw attraction range (using sphere for better visibility)
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, attractionRange);
        
        // Draw line to player if being attracted
        if (isBeingAttracted && playerTransform != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawLine(transform.position, playerTransform.position);
        }
    }
    
    private void OnDrawGizmosSelected()
    {
        // Always show range when selected
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireSphere(transform.position, attractionRange);
        
        // Show movement speed as an arrow
        if (playerTransform != null)
        {
            Vector3 direction = (playerTransform.position - transform.position).normalized;
            Gizmos.color = Color.blue;
            Gizmos.DrawRay(transform.position, direction * (moveSpeed * 0.5f));
        }
    }
    
    #endregion
}
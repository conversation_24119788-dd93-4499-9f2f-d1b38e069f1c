using UnityEngine;
using UnityEngine.InputSystem;
using Sirenix.OdinInspector;

[RequireComponent(typeof(Rigidbody2D))]
[RequireComponent(typeof(SpriteAnimator))]
[RequireComponent(typeof(SpriteRenderer))]
[RequireComponent(typeof(SpatialCollider))]
public class PlayerController : MonoBeh<PERSON>our
{
    [Title("Movement Settings")]
    [FoldoutGroup("Movement")]
    [SerializeField, Range(1f, 20f)]
    [Tooltip("Base movement speed in units per second")]
    private float moveSpeed = 5f;
    
    [FoldoutGroup("Movement")]
    [SerializeField, Range(1.1f, 3f)]
    [Tooltip("Speed multiplier when sprinting")]
    private float sprintMultiplier = 1.5f;
    
    [FoldoutGroup("Movement")]
    [SerializeField, Range(0f, 1f)]
    [Tooltip("Movement smoothing - higher values = more responsive")]
    private float movementSmoothing = 0.1f;
    
    [Title("Debug Settings")]
    [FoldoutGroup("Debug")]
    [SerializeField]
    [Tooltip("Show movement direction gizmos")]
    private bool showDebugGizmos = false;
    
    [FoldoutGroup("Debug")]
    [SerializeField, ShowIf("showDebugGizmos")]
    [Tooltip("Color for movement direction indicator")]
    private Color debugGizmoColor = Color.green;
    
    [Title("Animation Settings")]
    [FoldoutGroup("Animation")]
    [SerializeField]
    [Tooltip("Name of idle animation")]
    private string idleAnimationName = "Idle";
    
    [FoldoutGroup("Animation")]
    [SerializeField]
    [Tooltip("Name of walk animation")]
    private string walkAnimationName = "Walk";
    
    [FoldoutGroup("Animation")]
    [SerializeField]
    [Tooltip("Name of sprint animation")]
    private string sprintAnimationName = "Sprint";
    
    [FoldoutGroup("Animation")]
    [SerializeField]
    [Tooltip("Minimum speed to trigger walk animation")]
    private float walkAnimationThreshold = 0.1f;
    
    [FoldoutGroup("Animation")]
    [SerializeField]
    [Tooltip("Flip sprite based on movement direction")]
    private bool enableSpriteFlipping = true;
    
    [FoldoutGroup("Animation")]
    [SerializeField]
    [Tooltip("Threshold for horizontal movement to trigger flip")]
    private float flipThreshold = 0.01f;
    
    [Title("UI References")]
    [FoldoutGroup("UI")]
    [SerializeField]
    [Tooltip("Reference to the inventory manager")]
    private InventoryManager inventoryManager;
    
    // Components
    private Rigidbody2D rb;
    private InputSystem_Actions playerInput;
    private SpriteAnimator animator;
    private SpriteRenderer spriteRenderer;
    private SpatialCollider spatialCollider;
    
    // Movement state
    private Vector2 moveInput;
    private Vector2 moveVelocity;
    private bool isSprinting;
    
    // Animation state
    private string currentAnimationState = "";
    private bool isFacingRight = true;
    
    // Stats reference
    private PlayerStats playerStats;
    
    // Properties for external access
    [ShowInInspector, ReadOnly, FoldoutGroup("Runtime Info")]
    public Vector2 CurrentVelocity => rb ? rb.linearVelocity : Vector2.zero;
    
    [ShowInInspector, ReadOnly, FoldoutGroup("Runtime Info")]
    public float CurrentSpeed => CurrentVelocity.magnitude;
    
    [ShowInInspector, ReadOnly, FoldoutGroup("Runtime Info")]
    public bool IsMoving => moveInput.sqrMagnitude > 0.01f;
    
    [ShowInInspector, ReadOnly, FoldoutGroup("Runtime Info")]
    public string FacingDirection => isFacingRight ? "Right" : "Left";
    
    void Awake()
    {
        rb = GetComponent<Rigidbody2D>();
        rb.gravityScale = 0f; // Top-down game, no gravity
        rb.linearDamping = 5f; // Add some natural deceleration
        rb.constraints = RigidbodyConstraints2D.FreezeRotation;
        
        animator = GetComponent<SpriteAnimator>();
        spriteRenderer = GetComponent<SpriteRenderer>();
        spatialCollider = GetComponent<SpatialCollider>();
        playerStats = PlayerManager.PlayerStats;
        
       
        
        InitializeInput();
    }
    
    void InitializeInput()
    {
        playerInput = new InputSystem_Actions();
        
        // Subscribe to input events
        playerInput.Player.Move.performed += OnMove;
        playerInput.Player.Move.canceled += OnMove;
        
        playerInput.Player.Sprint.performed += OnSprintStart;
        playerInput.Player.Sprint.canceled += OnSprintEnd;
        
        playerInput.Player.OpenInventory.performed += OnOpenInventory;
    }
    
    void OnEnable()
    {
        playerInput?.Enable();
    }
    
    void OnDisable()
    {
        playerInput?.Disable();
    }
    
    void OnDestroy()
    {
        // Unsubscribe from events to prevent memory leaks
        if (playerInput != null)
        {
            playerInput.Player.Move.performed -= OnMove;
            playerInput.Player.Move.canceled -= OnMove;
            playerInput.Player.Sprint.performed -= OnSprintStart;
            playerInput.Player.Sprint.canceled -= OnSprintEnd;
            playerInput.Player.OpenInventory.performed -= OnOpenInventory;
            playerInput.Dispose();
        }
    }
    
    void OnMove(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }
    
    void OnSprintStart(InputAction.CallbackContext context)
    {
        isSprinting = true;
    }
    
    void OnSprintEnd(InputAction.CallbackContext context)
    {
        isSprinting = false;
    }
    
    void OnOpenInventory(InputAction.CallbackContext context)
    {
        if (inventoryManager != null)
        {
            inventoryManager.ToggleInventory();
        }
        else
        {
            Debug.LogWarning("Inventory Manager not assigned to Player Controller!");
        }
    }
    
    void Update()
    {
        UpdateFacingDirection();
        UpdateAnimationState();
    }
    
    void FixedUpdate()
    {
        HandleMovement();
    }
    
    void HandleMovement()
    {
        // Calculate target velocity with stat modifiers
        float speedMultiplier = playerStats?.GetCalculatedStat(StatType.MoveSpeed) ?? 1f;
        float currentSpeed = moveSpeed * speedMultiplier * (isSprinting ? sprintMultiplier : 1f);
        Vector2 targetVelocity = moveInput * currentSpeed;
        
        // Apply movement smoothing
        moveVelocity = Vector2.Lerp(moveVelocity, targetVelocity, 1f - movementSmoothing);
        
        // Apply velocity to rigidbody
        rb.linearVelocity = moveVelocity;
    }
    
    void UpdateFacingDirection()
    {
        if (!enableSpriteFlipping || !IsMoving) return;
        
        // Determine facing direction based on horizontal movement
        // In a top-down game, we primarily care about left/right movement
        if (moveInput.x > flipThreshold && !isFacingRight)
        {
            // Moving right but facing left - flip
            isFacingRight = true;
            Vector3 scale = transform.localScale;
            scale.x = Mathf.Abs(scale.x);
            transform.localScale = scale;
        }
        else if (moveInput.x < -flipThreshold && isFacingRight)
        {
            // Moving left but facing right - flip
            isFacingRight = false;
            Vector3 scale = transform.localScale;
            scale.x = -Mathf.Abs(scale.x);
            transform.localScale = scale;
        }
    }
    
    void UpdateAnimationState()
    {
        if (animator == null) return;
        
        string targetAnimation = idleAnimationName;
        
        // Determine animation based on movement state
        if (CurrentSpeed > walkAnimationThreshold)
        {
            targetAnimation = isSprinting ? sprintAnimationName : walkAnimationName;
        }
        
        // Only change animation if it's different
        if (targetAnimation != currentAnimationState)
        {
            currentAnimationState = targetAnimation;
            
            // Play animation if it exists
            if (animator.HasAnimation(targetAnimation))
            {
                animator.Play(targetAnimation);
            }
        }
    }
    
    // Editor methods
    [Button("Reset Movement Settings", ButtonSizes.Medium), FoldoutGroup("Movement")]
    void ResetMovementSettings()
    {
        moveSpeed = 5f;
        sprintMultiplier = 1.5f;
        movementSmoothing = 0.1f;
    }
    
    #if UNITY_EDITOR
    void OnDrawGizmos()
    {
        if (!showDebugGizmos || !Application.isPlaying) return;
        
        // Draw movement direction
        if (IsMoving)
        {
            Gizmos.color = debugGizmoColor;
            Vector3 direction = new Vector3(moveInput.x, moveInput.y, 0f).normalized;
            Gizmos.DrawRay(transform.position, direction * 2f);
            Gizmos.DrawWireSphere(transform.position + direction * 2f, 0.2f);
        }
        
        // Draw velocity
        if (rb != null && rb.linearVelocity.sqrMagnitude > 0.01f)
        {
            Gizmos.color = Color.yellow;
            Vector3 velocity = new Vector3(rb.linearVelocity.x, rb.linearVelocity.y, 0f).normalized;
            Gizmos.DrawRay(transform.position, velocity * 1.5f);
        }
    }
    #endif
}

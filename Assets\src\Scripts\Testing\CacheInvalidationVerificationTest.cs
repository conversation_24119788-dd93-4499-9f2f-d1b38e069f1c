using UnityEngine;
using System.Collections.Generic;
using System.Reflection;
using Sirenix.OdinInspector;

/// <summary>
/// Comprehensive test to verify cached autonomous support data is properly invalidated
/// when equipment changes through all possible pathways
/// </summary>
public class CacheInvalidationVerificationTest : MonoBehaviour
{
    [Title("Test Configuration")]
    [SerializeField] private SupportGemData autonomousSupportGem;
    [SerializeField] private SupportGemData nonAutonomousSupportGem;
    [SerializeField] private SkillGemData testSkillGem;
    
    [Title("Test Results")]
    [ShowInInspector, ReadOnly] private bool allTestsPassed = false;
    [ShowInInspector, ReadOnly] private string lastTestResult = "";
    [ShowInInspector, ReadOnly] private List<string> testResults = new List<string>();
    
    [Button("Run Complete Cache Invalidation Verification")]
    private void RunCompleteVerification()
    {
        UnityEngine.Debug.Log("=== Starting Complete Cache Invalidation Verification ===");
        testResults.Clear();
        
        bool test1 = TestEquipmentPanelCacheInvalidation();
        bool test2 = TestGemManagerCacheInvalidation();
        bool test3 = TestLevelUpCacheInvalidation();
        bool test4 = TestRuntimeGemSwappingCacheInvalidation();
        bool test5 = TestEdgeCaseCacheInvalidation();
        bool test6 = TestAutonomousDataConsistency();
        
        allTestsPassed = test1 && test2 && test3 && test4 && test5 && test6;
        
        if (allTestsPassed)
        {
            lastTestResult = "✅ ALL CACHE INVALIDATION TESTS PASSED";
            UnityEngine.Debug.Log("<color=green>" + lastTestResult + "</color>");
        }
        else
        {
            lastTestResult = "❌ SOME CACHE INVALIDATION TESTS FAILED";
            UnityEngine.Debug.LogError(lastTestResult);
        }
        
        // Print summary
        UnityEngine.Debug.Log("=== Test Results Summary ===");
        foreach (var result in testResults)
        {
            UnityEngine.Debug.Log(result);
        }
        
        UnityEngine.Debug.Log("=== Cache Invalidation Verification Complete ===");
    }
    
    [Button("Test Equipment Panel Cache Invalidation")]
    private bool TestEquipmentPanelCacheInvalidation()
    {
        UnityEngine.Debug.Log("Testing Equipment Panel UI cache invalidation...");
        
        var equipmentPanel = FindFirstObjectByType<EquipmentPanel>();
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        var gemManager = FindFirstObjectByType<GemManager>();
        
        if (equipmentPanel == null || skillExecutor == null || gemManager == null)
        {
            string error = "Missing required components for equipment panel test";
            testResults.Add("❌ Equipment Panel Test: " + error);
            UnityEngine.Debug.LogError(error);
            return false;
        }
        
        if (autonomousSupportGem == null || testSkillGem == null)
        {
            string error = "Missing test gems for equipment panel test";
            testResults.Add("❌ Equipment Panel Test: " + error);
            UnityEngine.Debug.LogError(error);
            return false;
        }
        
        try
        {
            // Test scenario: Equip skill gem, then support gem through equipment panel
            var skillInstance = new GemInstance(testSkillGem, GemRarity.Common);
            var supportInstance = new GemInstance(autonomousSupportGem, GemRarity.Common);
            
            // Add to inventory
            gemManager.AddGemInstanceToInventory(skillInstance);
            gemManager.AddGemInstanceToInventory(supportInstance);
            
            // Equip skill gem
            gemManager.EquipSkillGem(0, skillInstance);
            
            // Check autonomous status before support gem
            bool hasAutonomousBefore = GetCachedAutonomousStatus(skillExecutor, 0);
            
            // Equip support gem
            gemManager.EquipSupportGem(0, supportInstance);
            
            // Check autonomous status after support gem
            bool hasAutonomousAfter = GetCachedAutonomousStatus(skillExecutor, 0);
            
            // Verify cache was invalidated and updated
            bool testPassed = !hasAutonomousBefore && hasAutonomousAfter;
            
            // Clean up
            gemManager.UnequipSkillGem(0);
            gemManager.RemoveGemFromInventory(skillInstance);
            gemManager.RemoveGemFromInventory(supportInstance);
            
            string result = testPassed ? "✅ Equipment Panel Test: PASSED" : "❌ Equipment Panel Test: FAILED";
            testResults.Add(result);
            UnityEngine.Debug.Log(result);
            return testPassed;
        }
        catch (System.Exception e)
        {
            string error = "Equipment Panel Test: Exception - " + e.Message;
            testResults.Add("❌ " + error);
            UnityEngine.Debug.LogError(error);
            return false;
        }
    }
    
    [Button("Test GemManager Cache Invalidation")]
    private bool TestGemManagerCacheInvalidation()
    {
        UnityEngine.Debug.Log("Testing GemManager direct cache invalidation...");
        
        var gemManager = FindFirstObjectByType<GemManager>();
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        
        if (gemManager == null || skillExecutor == null)
        {
            string error = "Missing GemManager or SkillExecutor";
            testResults.Add("❌ GemManager Test: " + error);
            return false;
        }
        
        if (autonomousSupportGem == null || testSkillGem == null)
        {
            string error = "Missing test gems";
            testResults.Add("❌ GemManager Test: " + error);
            return false;
        }
        
        try
        {
            // Test all GemManager methods that should trigger cache invalidation
            var skillInstance = new GemInstance(testSkillGem, GemRarity.Common);
            var supportInstance = new GemInstance(autonomousSupportGem, GemRarity.Common);
            var supportInstance2 = new GemInstance(autonomousSupportGem, GemRarity.Common);
            
            gemManager.AddGemInstanceToInventory(skillInstance);
            gemManager.AddGemInstanceToInventory(supportInstance);
            gemManager.AddGemInstanceToInventory(supportInstance2);
            
            // Test EquipSkillGem
            gemManager.EquipSkillGem(0, skillInstance);
            
            // Test EquipSupportGem
            gemManager.EquipSupportGem(0, supportInstance);
            gemManager.EquipSupportGem(0, supportInstance2);
            
            // Test SwapSupportGems (should trigger cache invalidation)
            gemManager.SwapSupportGems(0, 0, 1);
            
            // Test RemoveSupportGemFromEquipment
            gemManager.RemoveSupportGemFromEquipment(0, supportInstance);
            
            // Test UnequipSupportGem
            gemManager.UnequipSupportGem(0, supportInstance2);
            
            // Test UnequipSkillGem
            gemManager.UnequipSkillGem(0);
            
            // Clean up
            gemManager.RemoveGemFromInventory(skillInstance);
            gemManager.RemoveGemFromInventory(supportInstance);
            gemManager.RemoveGemFromInventory(supportInstance2);
            
            string result = "✅ GemManager Test: PASSED (all methods executed without errors)";
            testResults.Add(result);
            UnityEngine.Debug.Log(result);
            return true;
        }
        catch (System.Exception e)
        {
            string error = "GemManager Test: Exception - " + e.Message;
            testResults.Add("❌ " + error);
            UnityEngine.Debug.LogError(error);
            return false;
        }
    }
    
    [Button("Test Level-up Cache Invalidation")]
    private bool TestLevelUpCacheInvalidation()
    {
        UnityEngine.Debug.Log("Testing level-up cache invalidation...");
        
        var playerStats = FindFirstObjectByType<PlayerStats>();
        if (playerStats == null)
        {
            string error = "PlayerStats not found";
            testResults.Add("❌ Level-up Test: " + error);
            return false;
        }
        
        try
        {
            // Store original level
            int originalLevel = playerStats.Level;
            
            // Trigger level up
            playerStats.AddXP(10000f); // Large amount to ensure level up
            
            // Check if level changed
            bool levelChanged = playerStats.Level > originalLevel;
            
            string result = levelChanged ? "✅ Level-up Test: PASSED" : "⚠️ Level-up Test: No level change detected";
            testResults.Add(result);
            UnityEngine.Debug.Log(result);
            return true; // Don't fail test if no level up occurred
        }
        catch (System.Exception e)
        {
            string error = "Level-up Test: Exception - " + e.Message;
            testResults.Add("❌ " + error);
            UnityEngine.Debug.LogError(error);
            return false;
        }
    }
    
    [Button("Test Runtime Gem Swapping Cache Invalidation")]
    private bool TestRuntimeGemSwappingCacheInvalidation()
    {
        UnityEngine.Debug.Log("Testing runtime gem swapping cache invalidation...");
        
        var gemManager = FindFirstObjectByType<GemManager>();
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        
        if (gemManager == null || skillExecutor == null)
        {
            string error = "Missing required components";
            testResults.Add("❌ Runtime Swapping Test: " + error);
            return false;
        }
        
        if (autonomousSupportGem == null || nonAutonomousSupportGem == null || testSkillGem == null)
        {
            string error = "Missing test gems (need autonomous and non-autonomous support gems)";
            testResults.Add("❌ Runtime Swapping Test: " + error);
            return false;
        }
        
        try
        {
            // Setup: Equip skill with non-autonomous support
            var skillInstance = new GemInstance(testSkillGem, GemRarity.Common);
            var nonAutoInstance = new GemInstance(nonAutonomousSupportGem, GemRarity.Common);
            var autoInstance = new GemInstance(autonomousSupportGem, GemRarity.Common);
            
            gemManager.AddGemInstanceToInventory(skillInstance);
            gemManager.AddGemInstanceToInventory(nonAutoInstance);
            gemManager.AddGemInstanceToInventory(autoInstance);
            
            gemManager.EquipSkillGem(0, skillInstance);
            gemManager.EquipSupportGem(0, nonAutoInstance);
            
            // Check autonomous status (should be false)
            bool hasAutonomousBefore = GetCachedAutonomousStatus(skillExecutor, 0);
            
            // Runtime swap: Replace non-autonomous with autonomous
            gemManager.UnequipSupportGem(0, nonAutoInstance);
            gemManager.EquipSupportGem(0, autoInstance);
            
            // Check autonomous status (should be true)
            bool hasAutonomousAfter = GetCachedAutonomousStatus(skillExecutor, 0);
            
            bool testPassed = !hasAutonomousBefore && hasAutonomousAfter;
            
            // Clean up
            gemManager.UnequipSkillGem(0);
            gemManager.RemoveGemFromInventory(skillInstance);
            gemManager.RemoveGemFromInventory(nonAutoInstance);
            gemManager.RemoveGemFromInventory(autoInstance);
            
            string result = testPassed ? "✅ Runtime Swapping Test: PASSED" : "❌ Runtime Swapping Test: FAILED";
            testResults.Add(result);
            UnityEngine.Debug.Log(result);
            return testPassed;
        }
        catch (System.Exception e)
        {
            string error = "Runtime Swapping Test: Exception - " + e.Message;
            testResults.Add("❌ " + error);
            UnityEngine.Debug.LogError(error);
            return false;
        }
    }
    
    [Button("Test Edge Case Cache Invalidation")]
    private bool TestEdgeCaseCacheInvalidation()
    {
        UnityEngine.Debug.Log("Testing edge case cache invalidation...");
        
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        if (skillExecutor == null)
        {
            string error = "SkillExecutor not found";
            testResults.Add("❌ Edge Case Test: " + error);
            return false;
        }
        
        try
        {
            // Test manual cache invalidation methods
            skillExecutor.InvalidateCache(0);
            skillExecutor.InvalidateAllCaches();
            skillExecutor.InvalidateSlotCache(0);
            skillExecutor.ClearAllCaches();
            
            // Test event system directly
            SkillCacheEvents.TriggerSkillGemChanged(0);
            SkillCacheEvents.TriggerSupportGemsChanged(0);
            SkillCacheEvents.TriggerAllEquipmentChanged();
            SkillCacheEvents.TriggerPlayerStatsChanged();
            SkillCacheEvents.TriggerPlayerBuffsChanged();
            
            string result = "✅ Edge Case Test: PASSED (all methods executed without errors)";
            testResults.Add(result);
            UnityEngine.Debug.Log(result);
            return true;
        }
        catch (System.Exception e)
        {
            string error = "Edge Case Test: Exception - " + e.Message;
            testResults.Add("❌ " + error);
            UnityEngine.Debug.LogError(error);
            return false;
        }
    }
    
    [Button("Test Autonomous Data Consistency")]
    private bool TestAutonomousDataConsistency()
    {
        UnityEngine.Debug.Log("Testing autonomous data consistency after cache operations...");
        
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        if (skillExecutor == null)
        {
            string error = "SkillExecutor not found";
            testResults.Add("❌ Consistency Test: " + error);
            return false;
        }
        
        try
        {
            // Test that cached values are consistent after invalidation
            skillExecutor.InvalidateAllCaches();
            
            // Multiple calls should return consistent results
            bool result1 = GetCachedAutonomousStatus(skillExecutor, 0);
            bool result2 = GetCachedAutonomousStatus(skillExecutor, 0);
            bool result3 = GetCachedAutonomousStatus(skillExecutor, 0);
            
            bool consistent = (result1 == result2) && (result2 == result3);
            
            string result = consistent ? "✅ Consistency Test: PASSED" : "❌ Consistency Test: FAILED";
            testResults.Add(result);
            UnityEngine.Debug.Log(result);
            return consistent;
        }
        catch (System.Exception e)
        {
            string error = "Consistency Test: Exception - " + e.Message;
            testResults.Add("❌ " + error);
            UnityEngine.Debug.LogError(error);
            return false;
        }
    }
    
    /// <summary>
    /// Helper method to get cached autonomous status using reflection
    /// </summary>
    private bool GetCachedAutonomousStatus(SkillExecutor skillExecutor, int slotIndex)
    {
        try
        {
            // Use reflection to access private cached autonomous status
            var field = typeof(SkillExecutor).GetField("_cachedAutonomousStatus", BindingFlags.NonPublic | BindingFlags.Instance);
            if (field != null)
            {
                var cache = field.GetValue(skillExecutor) as Dictionary<int, bool>;
                if (cache != null && cache.TryGetValue(slotIndex, out bool cachedValue))
                {
                    return cachedValue;
                }
            }
            
            // If no cached value, call the method to populate cache
            var equipmentPanel = FindFirstObjectByType<EquipmentPanel>();
            if (equipmentPanel != null)
            {
                var controller = equipmentPanel.GetActiveSkillController(slotIndex);
                if (controller != null)
                {
                    // This will populate the cache
                    var method = typeof(SkillExecutor).GetMethod("HasAutonomousSupport", BindingFlags.NonPublic | BindingFlags.Instance);
                    if (method != null)
                    {
                        return (bool)method.Invoke(skillExecutor, new object[] { controller, slotIndex });
                    }
                }
            }
            
            return false;
        }
        catch
        {
            return false;
        }
    }
}

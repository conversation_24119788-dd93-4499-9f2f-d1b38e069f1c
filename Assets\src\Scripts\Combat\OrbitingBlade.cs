using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections.Generic;

[RequireComponent(typeof(SpatialCollider))]
public class OrbitingBlade : MonoBehaviour, ISpawnable, ISpatialCollisionHandler
{
    [Title("Blade Settings")]
    [SerializeField] private float defaultDuration = 10f;
    [SerializeField] private float baseDamage = 15f;
    [SerializeField] private float defaultOrbitRadius = 2f;
    [SerializeField] private float defaultRotationSpeed = 180f; // degrees per second
    
    [Title("Blade Behavior")]
    [SerializeField] private float damageInterval = 0.5f; // How often the blade can damage the same target
    [<PERSON>lt<PERSON>("How fast the blade adjusts to player movement")]
    [SerializeField] private float followSpeed = 8f;
    
    [Title("Particle Effects")]
    [SerializeField] private bool useTrailParticles = true;
    [ShowIf("useTrailParticles")]
    [SerializeField] private ParticleType trailParticleType = ParticleType.FireTrail;
    [ShowIf("useTrailParticles")]
    [SerializeField] private float trailInterval = 0.1f;
    
    [SerializeField] private bool useDespawnParticles = true;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private ParticleType despawnParticleType = ParticleType.SmokeImpact;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private int despawnParticleCount = 8;
    
    [InfoBox("Optional: Assign a child transform for precise particle spawn position")]
    [SerializeField] private Transform particleSpawnPoint;
    
    // Runtime values
    public float damage { get; set; }
    public float duration { get; set; }
    public float orbitRadius { get; set; }
    public float rotationSpeed { get; set; }
    public float critChance { get; set; }
    public float critMultiplier { get; set; }
    public DamageType damageType { get; set; } = DamageType.Physical;
    public float ailmentChance { get; set; } = 0f;

    // Gem data for status effect configuration
    public SkillGemData skillGemData { get; set; }
    public List<GemInstance> supportGems { get; set; }
    
    // Damage breakdown for type-specific damage
    public DamageBreakdown? damageBreakdown { get; set; }
    
    // Support gem effects - adapted for melee
    private bool hasAreaDamage;
    private float areaRadius;
    
    // Orbiting state
    private Transform _playerTransform;
    private Vector3 _currentOrbitCenter;
    private float _currentAngle;
    private float _timeAlive;
    private SpatialCollider _spatialCollider;
    private bool _isActive;
    private CollisionLayers _currentLayer;
    
    // Damage tracking to prevent spam
    private readonly Dictionary<GameObject, float> _lastDamageTime = new Dictionary<GameObject, float>();
    
    // Reusable list to eliminate GC allocation in CleanupDamageTimers
    private readonly List<GameObject> _reusableKeysToRemove = new List<GameObject>();
    
    // Cache for nearby enemies to eliminate allocations in collision detection
    private readonly List<ICollidable> _nearbyEnemiesCache = new List<ICollidable>(32);
    
    private void Awake()
    {
        _spatialCollider = GetComponent<SpatialCollider>();
    }
    
    public void Initialize(Vector3 spawnPosition, float startAngle, float damageValue = 1f, CollisionLayers layer = CollisionLayers.PlayerProjectile)
    {
        Initialize(spawnPosition, startAngle, damageValue, layer, defaultDuration, defaultOrbitRadius, defaultRotationSpeed);
    }
    
    public void Initialize(Vector3 spawnPosition, float startAngle, float damageValue, CollisionLayers layer, 
                          float bladeDuration, float orbitRadiusValue, float rotationSpeedValue)
    {
        // Find player reference
        if (PlayerManager.PlayerGameObject != null)
        {
            _playerTransform = PlayerManager.PlayerGameObject.transform;
        }
        else
        {
            Debug.LogWarning("[OrbitingBlade] PlayerManager.PlayerGameObject is null! Blade will not orbit correctly.");
        }
        
        _currentOrbitCenter = spawnPosition;
        _currentAngle = startAngle;
        _timeAlive = 0f;
        
        this.damage = damageValue;
        this.duration = bladeDuration;
        this.orbitRadius = orbitRadiusValue;
        this.rotationSpeed = rotationSpeedValue;
        
        _isActive = true;
        
        _spatialCollider.SetLayer(layer);
        _currentLayer = layer;
        
        // Position blade at initial angle
        UpdateBladePosition();
        
        // Start trail particles if enabled
        if (useTrailParticles && ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.StartContinuousEffect(trailParticleType, transform, trailInterval);
        }
    }
    
    private void Update()
    {
        if (!_isActive) return;
        
        _timeAlive += Time.deltaTime;
        
        // Check if blade has expired
        if (_timeAlive >= duration)
        {
            SpawnDespawnParticles();
            Deactivate();
            return;
        }
        
        // Update orbit center to follow player smoothly
        if (_playerTransform != null)
        {
            _currentOrbitCenter = Vector3.Lerp(_currentOrbitCenter, _playerTransform.position, followSpeed * Time.deltaTime);
        }
        
        // Update rotation angle
        _currentAngle += rotationSpeed * Time.deltaTime;
        if (_currentAngle >= 360f) _currentAngle -= 360f;
        
        // Update blade position around orbit center
        UpdateBladePosition();
        
        // Clean up old damage timers
        CleanupDamageTimers();
    }
    
    private void UpdateBladePosition()
    {
        float radians = _currentAngle * Mathf.Deg2Rad;
        Vector3 offset = new Vector3(Mathf.Cos(radians), Mathf.Sin(radians), 0f) * orbitRadius;
        transform.position = _currentOrbitCenter + offset;
        
        // Rotate blade to face tangent direction (perpendicular to radius)
        float tangentAngle = _currentAngle + 90f;
        transform.rotation = Quaternion.AngleAxis(tangentAngle, Vector3.forward);
    }
    
    // ISpatialCollisionHandler implementation
    public void HandleCollisionEnter(CollisionInfo collision)
    {
        ApplyDamage(collision.Other.GameObject);
    }
    
    public void HandleCollisionStay(CollisionInfo collision)
    {
        ApplyDamage(collision.Other.GameObject);
    }
    
    public void HandleCollisionExit(CollisionInfo collision) { }
    public void HandleTriggerEnter(CollisionInfo collision) { }
    public void HandleTriggerStay(CollisionInfo collision) { }
    public void HandleTriggerExit(CollisionInfo collision) { }
    
    private bool ApplyDamage(GameObject target)
    {
        // Safety check: Don't apply damage if we have 0 damage
        if (damage <= 0f)
        {
            return false;
        }
        
        // Check damage interval to prevent spam
        float currentTime = Time.time;
        if (_lastDamageTime.TryGetValue(target, out float lastTime))
        {
            if (currentTime - lastTime < damageInterval)
            {
                return false; // Too soon to damage this target again
            }
        }
        
        // Update damage time
        _lastDamageTime[target] = currentTime;
        
        // Calculate final damage with crit
        float finalDamage = damage;
        bool isCrit = Random.Range(0f, 100f) < critChance;
        if (isCrit)
        {
            finalDamage *= critMultiplier;
        }

        // Create damage info with gem data for status effect configuration
        DamageInfo damageInfo;
        if (damageBreakdown.HasValue)
        {
            // Use damage breakdown if available (includes conversion info)
            damageInfo = DamageInfo.FromBreakdown(
                damageBreakdown.Value,
                isCrit,
                critMultiplier,
                "OrbitingBlade",
                ailmentChance,
                skillGemData,
                supportGems
            );
        }
        else if (skillGemData != null)
        {
            // Player skill without breakdown - this should not happen!
            Debug.LogError($"[OrbitingBlade] Player skill '{skillGemData.gemName}' missing damage breakdown! Support gem effects like Brutality Support will be bypassed.");
            return false;
        }
        else
        {
            // Agent/Enemy skill fallback - use single damage type
            damageInfo = DamageInfo.FromSingleType(
                Mathf.RoundToInt(finalDamage),
                damageType,
                isCrit,
                critMultiplier,
                "OrbitingBlade",
                ailmentChance,
                skillGemData,
                supportGems
            );
        }
        
        // Apply damage to target
        bool damageApplied = false;
        
        // Handle player damage via PlayerManager
        if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
        {
            PlayerManager.DealDamageToPlayer(damageInfo);
            damageApplied = true;
        }
        // Check for CombatantHealth first (enemies use this)
        else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
        {
            combatantHealth.TakeDamage(damageInfo);
            damageApplied = true;
        }
        // Fall back to HealthComponent for other targets
        else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
        {
            healthComponent.TakeDamage(damageInfo);
            damageApplied = true;
        }
        
        // Handle area damage if enabled
        if (damageApplied && hasAreaDamage)
        {
            ApplyAreaDamage(transform.position);
        }
        
        return damageApplied;
    }
    
    private void ApplyAreaDamage(Vector3 center)
    {
        if (CollisionManager.Instance == null) return;
        
        // Use NonAlloc method to avoid allocations and race conditions
        CollisionManager.Instance.GetCollidersInRadiusNonAlloc(
            center, areaRadius, _nearbyEnemiesCache, CollisionLayers.Enemy);
            
        foreach (var collidable in _nearbyEnemiesCache)
        {
            var target = collidable.GameObject;
            if (target == gameObject) continue; // Skip self
            
            // Check damage interval for area damage too
            float currentTime = Time.time;
            if (_lastDamageTime.TryGetValue(target, out float lastTime))
            {
                if (currentTime - lastTime < damageInterval)
                {
                    continue; // Too soon to damage this target again
                }
            }
            
            // Apply reduced damage for area effect (50% of base)
            float areaDamage = damage * 0.5f;
            bool isCrit = Random.Range(0f, 100f) < critChance;
            if (isCrit)
            {
                areaDamage *= critMultiplier;
            }
            
            // Create damage info for area damage
            DamageInfo areaDamageInfo;
            if (damageBreakdown.HasValue)
            {
                // Scale damage breakdown for area damage
                var scaledBreakdown = damageBreakdown.Value;
                float areaMultiplier = 0.5f;
                scaledBreakdown.physicalDamage *= areaMultiplier;
                scaledBreakdown.fireDamage *= areaMultiplier;
                scaledBreakdown.iceDamage *= areaMultiplier;
                scaledBreakdown.lightningDamage *= areaMultiplier;

                areaDamageInfo = DamageInfo.FromBreakdown(
                    scaledBreakdown,
                    isCrit,
                    critMultiplier,
                    "OrbitingBlade_Area",
                    ailmentChance,
                    skillGemData,
                    supportGems
                );
            }
            else if (skillGemData != null)
            {
                // Player skill without breakdown - this should not happen!
                Debug.LogError($"[OrbitingBlade] Player skill '{skillGemData.gemName}' missing damage breakdown for area damage! Support gem effects like Brutality Support will be bypassed.");
                return;
            }
            else
            {
                // Agent/Enemy skill fallback - use single damage type for area damage
                areaDamageInfo = DamageInfo.FromSingleType(
                    Mathf.RoundToInt(areaDamage),
                    damageType,
                    isCrit,
                    critMultiplier,
                    "OrbitingBlade_Area",
                    ailmentChance,
                    skillGemData,
                    supportGems
                );
            }
            
            // Update damage time for area damage
            _lastDamageTime[target] = currentTime;
            
            // Apply area damage
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                PlayerManager.DealDamageToPlayer(areaDamageInfo);
            }
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
            {
                combatantHealth.TakeDamage(areaDamageInfo);
            }
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
            {
                healthComponent.TakeDamage(areaDamageInfo);
            }
        }
    }
    
    private void CleanupDamageTimers()
    {
        // Remove entries older than damage interval to prevent memory leak
        float cutoffTime = Time.time - damageInterval - 1f; // Extra buffer
        
        // Clear and reuse the list to eliminate GC allocation
        _reusableKeysToRemove.Clear();
        
        foreach (var kvp in _lastDamageTime)
        {
            if (kvp.Value < cutoffTime || kvp.Key == null)
            {
                _reusableKeysToRemove.Add(kvp.Key);
            }
        }
        
        foreach (var key in _reusableKeysToRemove)
        {
            _lastDamageTime.Remove(key);
        }
    }
    
    private void SpawnDespawnParticles()
    {
        if (!useDespawnParticles || ParticleEffectManager.Instance == null) return;
        
        Vector3 spawnPosition = particleSpawnPoint != null ? particleSpawnPoint.position : transform.position;
        ParticleEffectManager.Instance.SpawnParticle(despawnParticleType, spawnPosition, despawnParticleCount);
    }
    
    private void Deactivate()
    {
        _isActive = false;
        
        // Stop trail particles if active
        if (useTrailParticles && ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
        
        if (PoolManager.Instance != null)
        {
            PoolManager.Instance.Despawn(gameObject);
        }
        else
        {
            gameObject.SetActive(false);
        }
    }
    
    // ISpawnable implementation
    public void OnSpawn()
    {
        _isActive = true;
    }
    
    public void OnDespawn()
    {
        _isActive = false;
        damage = 0f;
        duration = defaultDuration;
        orbitRadius = defaultOrbitRadius;
        rotationSpeed = defaultRotationSpeed;
        damageType = DamageType.Physical;
        ailmentChance = 0f;
        damageBreakdown = null;

        // Clear gem data references
        skillGemData = null;
        supportGems = null;
        
        // Reset support gem effects
        hasAreaDamage = false;
        areaRadius = 0f;
        
        // Clear damage tracking and reusable collections
        _lastDamageTime.Clear();
        _reusableKeysToRemove.Clear();
        
        // Reset orbiting state
        _playerTransform = null;
        _currentAngle = 0f;
        _timeAlive = 0f;
        
        // Ensure particles are stopped
        if (ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
    }
    
    // Support gem effect setters
    public void SetAreaDamage(bool enable, float radius = 1.5f)
    {
        hasAreaDamage = enable;
        areaRadius = radius;
    }
    
    // Orbiting-specific configuration
    public void SetOrbitParameters(float radius, float speed, float followSpeedValue = 8f)
    {
        orbitRadius = radius;
        rotationSpeed = speed;
        followSpeed = followSpeedValue;
    }
}
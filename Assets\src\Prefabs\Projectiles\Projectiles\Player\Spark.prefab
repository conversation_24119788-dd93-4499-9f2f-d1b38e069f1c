%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2340940985034927489
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5232708968013337809}
  - component: {fileID: 6259352577009458110}
  - component: {fileID: 7994387764726345637}
  - component: {fileID: 2151820075720376401}
  m_Layer: 0
  m_Name: Spark
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5232708968013337809
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2340940985034927489}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.6, y: 0.6, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8011131586517014002}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6259352577009458110
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2340940985034927489}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61884fc7ad71f0244b1e1b0fe955a57f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::Game.Systems.CollisionSystem.SpatialCollider
  shape: 0
  radius: 0.15
  size: {x: 0.3, y: 0.3}
  colliderOffset: {x: 0, y: 0}
  layer: 4
  isTrigger: 0
  drawGizmos: 1
  gizmoColor: {r: 1, g: 1, b: 1, a: 1}
--- !u!114 &7994387764726345637
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2340940985034927489}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dcda6c75a7fde1b4aa9e783b4e678706, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::SpriteAnimator
  defaultAnimation: {fileID: 11400000, guid: e92f8b08a63391d4b9db4d758ec6326f, type: 2}
  playOnStart: 1
  globalSpeedMultiplier: 1
  rendererMode: 2
  manualRenderers:
  - {fileID: 7197789697882582287}
  includeInactiveChildren: 0
  childTagFilter: 
  animations: []
--- !u!114 &2151820075720376401
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2340940985034927489}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 176dffed252ca0e4991bafe887d80158, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::SerpentineProjectile
  defaultSpeed: 10
  defaultLifetime: 2
  baseDamage: 10
  waveAmplitude: 0.01
  waveFrequency: 2
  serpentineDelay: 0.2
  enableForkDebugLogging: 0
  useImpactParticles: 1
  impactParticleType: 1
  impactParticleCount: 10
  useTrailParticles: 0
  trailParticleType: 4
  trailInterval: 0.1
  useDespawnParticles: 0
  despawnParticleType: 2
  despawnParticleCount: 5
  particleSpawnPoint: {fileID: 0}
--- !u!1 &3638840608215621978
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8011131586517014002}
  - component: {fileID: 7197789697882582287}
  m_Layer: 0
  m_Name: SparkVisual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8011131586517014002
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3638840608215621978}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5232708968013337809}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &7197789697882582287
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3638840608215621978}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 2
  m_Sprite: {fileID: -2413806693520163455, guid: a86470a33a6bf42c4b3595704624658b, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0

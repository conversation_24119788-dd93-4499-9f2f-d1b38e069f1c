using UnityEngine;
using PrimeTween;
using Sirenix.OdinInspector;

/// <summary>
/// Handles splinter pickup behavior with magnetic attraction.
/// Follows the established pickup pattern from CurrencyPickup.
/// </summary>
public class SplinterPickup : MonoBehaviour, ISpawnable, ISpatialCollisionHandler
{
    #region Configuration
    
    [Title("Pickup Configuration")]
    [SerializeField]
    [InfoBox("Magnetic attraction settings")]
    private float magneticRange = 3f;
    
    [SerializeField]
    private float attractionSpeed = 8f;
    
    [Title("Animation Settings")]
    [SerializeField, Range(0.1f, 2f)]
    [Tooltip("Duration of the pickup animation")]
    private float pickupAnimationDuration = 0.3f;
    
    [SerializeField, Range(0.5f, 2f)]
    [Tooltip("Scale multiplier during pickup animation")]
    private float pickupScaleMultiplier = 1.2f;
    
    [SerializeField, Range(0.1f, 1f)]
    [Tooltip("Duration of the spawn scale-up animation")]
    private float spawnAnimationDuration = 0.25f;
    
    [SerializeField]
    [Tooltip("Ease type for spawn animation")]
    private Ease spawnAnimationEase = Ease.OutBack;
    
    [Title("Audio")]
    [SerializeField]
    [InfoBox("Sound effect for pickup (optional)")]
    private AudioClip pickupSound;
    
    [SerializeField, Range(0f, 1f)]
    private float pickupVolume = 0.5f;
    
    [Title("Debug")]
    [SerializeField]
    private bool enableDebugLogging = false;
    
    #endregion
    
    #region Components
    
    [Title("Required Components")]
    [Required]
    [SerializeField]
    private SpriteRenderer spriteRenderer;
    
    [Required]
    [SerializeField] 
    private SpatialCollider spatialCollider;
    
    [ShowInInspector, ReadOnly]
    private Transform playerTransform;
    
    // Cached Components
    private Transform cachedTransform;
    private Vector3 originalScale;
    private Color originalColor;
    
    #endregion
    
    #region State
    
    [Title("Current State")]
    [ShowInInspector, ReadOnly]
    private SplinterType currentSplinterType = SplinterType.None;
    
    [ShowInInspector, ReadOnly]
    private int splinterAmount = 1;
    
    [ShowInInspector, ReadOnly]
    private bool isBeingCollected = false;
    
    [ShowInInspector, ReadOnly]
    private bool isInitialized = false;
    
    private Vector3 targetPosition;
    
    // State Management
    private bool isBeingAttracted = false;
    private float attractionStartTime;
    private Vector3 attractionStartPosition;
    
    // Animation
    private Sequence pickupTween;
    private Tween spawnTween;
    
    #endregion
    
    #region Unity Lifecycle
    
    private void Awake()
    {
        ValidateComponents();
        CachePlayerReference();
    }
    
    private void Update()
    {
        if (!isInitialized || isBeingCollected || playerTransform == null)
            return;
        
        HandleMagneticAttraction();
    }
    
    private void OnDestroy()
    {
        // Clean up tweens
        StopAllTweens();
    }
    
    #endregion
    
    #region ISpawnable Implementation
    
    public void OnSpawn()
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[SplinterPickup] OnSpawn called");
        }
        
        // Reset state
        isBeingCollected = false;
        isInitialized = false;
        
        // Reset state
        isBeingAttracted = false;
        
        // Start with zero scale for spawn animation
        cachedTransform.localScale = Vector3.zero;
        
        // Ensure sprite renderer is properly initialized and visible
        if (spriteRenderer != null)
        {
            // If original color is not set (black/transparent), use white with full alpha
            if (originalColor == Color.clear || originalColor.a == 0f)
            {
                originalColor = Color.white;
            }
            
            // Reset visual state to original with full alpha
            spriteRenderer.color = originalColor;
            
            // Ensure sprite is visible
            if (spriteRenderer.color.a == 0f)
            {
                Color visibleColor = spriteRenderer.color;
                visibleColor.a = 1f;
                spriteRenderer.color = visibleColor;
                originalColor = visibleColor;
            }
        }
        
        // Enable spatial collider
        if (spatialCollider != null)
        {
            spatialCollider.enabled = true;
        }
        
        // Stop any existing tweens
        StopAllTweens();
        
        // Play spawn scale-up animation
        PlaySpawnAnimation();
        
        // Cache player reference if needed
        CachePlayerReference();
    }
    
    public void OnDespawn()
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[SplinterPickup] OnDespawn called");
        }
        
        // Stop all animations
        StopAllTweens();
        
        // Clean up state
        isBeingCollected = false;
        isInitialized = false;
        currentSplinterType = SplinterType.None;
        splinterAmount = 0;
        cachedTransform.localScale = originalScale;
        
        // Disable spatial collider
        if (spatialCollider != null)
        {
            spatialCollider.enabled = false;
        }
    }
    
    #endregion
    
    #region ISpatialCollisionHandler Implementation
    
    public void HandleCollisionEnter(CollisionInfo collision)
    {
        // Not used for trigger-based pickup
    }
    
    public void HandleCollisionStay(CollisionInfo collision)
    {
        // Not used for trigger-based pickup
    }
    
    public void HandleCollisionExit(CollisionInfo collision)
    {
        // Not used for trigger-based pickup
    }
    
    public void HandleTriggerEnter(CollisionInfo collision)
    {
        if (isBeingCollected || !isInitialized)
            return;
            
        if (collision.Other.Layer.HasFlag(CollisionLayers.Player))
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SplinterPickup] Trigger detected with player - initiating collection");
            }
            
            CollectSplinter();
        }
    }
    
    public void HandleTriggerStay(CollisionInfo collision)
    {
        // Not needed for splinter pickup
    }
    
    public void HandleTriggerExit(CollisionInfo collision)
    {
        // Not needed for splinter pickup
    }
    
    #endregion
    
    #region Public API
    
    /// <summary>
    /// Configures the splinter pickup with type, amount, and position
    /// </summary>
    public void ConfigurePickup(SplinterType splinterType, int amount, Vector3 worldPosition)
    {
        if (!splinterType.IsSingleType() || amount <= 0)
        {
            Debug.LogError($"[SplinterPickup] Invalid configuration: Type={splinterType}, Amount={amount}");
            return;
        }
        
        currentSplinterType = splinterType;
        splinterAmount = amount;
        transform.position = worldPosition;
        
        // Set visual appearance
        ConfigureVisualAppearance();
        
        // Mark as initialized
        isInitialized = true;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[SplinterPickup] Configured: {amount}x {splinterType} at {worldPosition}");
        }
    }
    
    #endregion
    
    #region Private Methods
    
    private void ValidateComponents()
    {
        // Cache components
        spatialCollider = GetComponent<SpatialCollider>();
        cachedTransform = transform;
        originalScale = cachedTransform.localScale;
        
        if (spriteRenderer == null)
        {
            spriteRenderer = GetComponent<SpriteRenderer>();
            if (spriteRenderer == null)
            {
                Debug.LogError("[SplinterPickup] SpriteRenderer component missing!", this);
            }
            else
            {
                // Cache original color - ensure it's not transparent/black
                originalColor = spriteRenderer.color;
                
                // If the sprite color is transparent or black, set it to white
                if (originalColor == Color.clear || originalColor.a == 0f || 
                    (originalColor.r == 0f && originalColor.g == 0f && originalColor.b == 0f))
                {
                    originalColor = Color.white;
                    spriteRenderer.color = originalColor;
                }
            }
        }
        
        // Configure spatial collider
        if (spatialCollider != null)
        {
            spatialCollider.SetLayer(CollisionLayers.Pickup);
            spatialCollider.SetTrigger(true);
        }
        else
        {
            Debug.LogError($"[SplinterPickup] SpatialCollider component is missing on {gameObject.name}");
        }
    }
    
    private void CachePlayerReference()
    {
        if (playerTransform == null)
        {
            var playerPosition = PlayerManager.GetPrimaryTargetPosition();
            if (playerPosition != Vector3.zero)
            {
                // Find player GameObject from position
                var playerController = FindFirstObjectByType<PlayerController>();
                if (playerController != null)
                {
                    playerTransform = playerController.transform;
                }
            }
        }
    }
    
    private void ConfigureVisualAppearance()
    {
        if (spriteRenderer == null) return;
        
        // DON'T change color - keep original sprite color
        // The splinter type is distinguished by the sprite itself, not color
        
        // Set sorting order to appear above ground
        spriteRenderer.sortingOrder = 10;
    }
    
    private void HandleMagneticAttraction()
    {
        if (playerTransform == null)
        {
            CachePlayerReference();
            return;
        }
        
        Vector3 targetPosition = PlayerManager.GetPrimaryTargetPosition();
        float distanceToPlayer = Vector3.Distance(cachedTransform.position, targetPosition);
        
        if (!isBeingAttracted && distanceToPlayer <= magneticRange)
        {
            StartAttractionToPlayer();
        }
        
        // Handle dynamic player tracking during attraction (same as CurrencyPickup)
        if (isBeingAttracted)
        {
            MoveTowardsPlayer(targetPosition, distanceToPlayer);
        }
    }
    
    private void CollectSplinter()
    {
        if (isBeingCollected) return;
        
        isBeingCollected = true;
        
        // Stop all movement immediately to prevent issues
        isBeingAttracted = false;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[SplinterPickup] Collecting {splinterAmount}x {currentSplinterType} splinters");
        }
        
        // Disable spatial collider immediately to prevent double collection
        if (spatialCollider != null)
        {
            spatialCollider.enabled = false;
        }
        
        // Add splinters to collection manager with better error handling
        bool success = false;
        try
        {
            success = SplinterCollectionManager.AddSplinters(currentSplinterType, splinterAmount);
            
            if (enableDebugLogging)
            {
                Debug.Log($"[SplinterPickup] AddSplinters result: {success} for {splinterAmount}x {currentSplinterType}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[SplinterPickup] Exception in AddSplinters: {e.Message}");
            success = false;
        }
        
        if (success)
        {
            // Play pickup sound
            PlayPickupSound();
            
            if (enableDebugLogging)
            {
                Debug.Log($"[SplinterPickup] Successfully collected {splinterAmount}x {currentSplinterType}, returning to pool");
            }
        }
        else
        {
            Debug.LogError($"[SplinterPickup] Failed to add {splinterAmount}x {currentSplinterType} splinters to collection manager");
        }
        
        // Always return to pool, regardless of success
        ReturnToPool();
    }
    
    private void PlayPickupSound()
    {
        if (pickupSound != null && pickupVolume > 0f)
        {
            AudioSource.PlayClipAtPoint(pickupSound, transform.position, pickupVolume);
        }
    }
    
    private void PlayPickupAnimation()
    {
        // Disable spatial collider to prevent double collection
        if (spatialCollider != null)
        {
            spatialCollider.enabled = false;
        }
        
        // Scale up briefly then disappear (same as CurrencyPickup)
        var sequence = Sequence.Create(useUnscaledTime: false);
        
        sequence
            .Group(Tween.Scale(
                cachedTransform,
                originalScale * pickupScaleMultiplier,
                pickupAnimationDuration * 0.3f,
                Ease.OutBack
            ))
            .Chain(Tween.Scale(
                cachedTransform,
                Vector3.zero,
                pickupAnimationDuration * 0.7f,
                Ease.InBack
            ))
            .OnComplete(() => {
                // Return to pool
                ReturnToPool();
            });
            
        pickupTween = sequence;
        
        // Don't fade out sprite - keep original color and alpha unchanged
    }
    
    private void StopAllTweens()
    {
        pickupTween.Stop();
        spawnTween.Stop();
    }
    
    /// <summary>
    /// Play spawn scale-up animation from zero to original scale
    /// </summary>
    /// <summary>
    /// Play spawn scale-up animation from zero to original scale
    /// </summary>
    private void PlaySpawnAnimation()
    {
        // Scale from zero to original scale with satisfying bounce effect
        spawnTween = Tween.Scale(
            cachedTransform,
            originalScale,
            spawnAnimationDuration,
            spawnAnimationEase,
            useUnscaledTime: false
        );
        
        if (enableDebugLogging)
        {
            Debug.Log("[SplinterPickup] Playing spawn scale-up animation");
        }
    }
    
    private void StartAttractionToPlayer()
    {
        if (isBeingAttracted || isBeingCollected || playerTransform == null)
            return;
            
        isBeingAttracted = true;
        attractionStartTime = Time.time;
        attractionStartPosition = cachedTransform.position;
        
        if (enableDebugLogging)
        {
            Debug.Log("[SplinterPickup] Starting attraction to player");
        }
    }
    
    /// <summary>
    /// Handles dynamic movement towards the player's current position with smooth InBack easing
    /// Same satisfying movement as CurrencyPickup!
    /// </summary>
    private void MoveTowardsPlayer(Vector3 targetPosition, float distanceToPlayer)
    {
        // Calculate time-based progress for the easing
        float elapsedTime = Time.time - attractionStartTime;
        float estimatedDuration = Vector3.Distance(attractionStartPosition, targetPosition) / attractionSpeed;
        float progress = Mathf.Clamp01(elapsedTime / estimatedDuration);
        
        // Get current player position for dynamic tracking
        Vector3 currentTarget = PlayerManager.GetPrimaryTargetPosition();
        
        // Ease.InBack implementation - continuous curve from start to finish
        // c1 = 1.70158, c3 = c1 + 1 = 2.70158
        const float c1 = 1.70158f;
        const float c3 = 2.70158f;
        
        // InBack formula: c3 * t^3 - c1 * t^2
        float easedProgress = c3 * progress * progress * progress - c1 * progress * progress;
        
        // Handle the back effect properly
        Vector3 directionToTarget = (currentTarget - attractionStartPosition).normalized;
        
        if (easedProgress < 0)
        {
            // "Back" phase: Move away from target - SUPER SATISFYING VERSION!
            float backIntensity = Mathf.Abs(easedProgress) * 2.0f; // Dramatic back effect
            
            // Add slight perpendicular curve for organic feel (like real physics)
            Vector3 perpendicular = new Vector3(-directionToTarget.y, directionToTarget.x, 0);
            float curve = Mathf.Sin(progress * Mathf.PI * 2f) * backIntensity * 0.15f; // Gentle arc during back
            
            Vector3 backPosition = attractionStartPosition - directionToTarget * backIntensity + perpendicular * curve;
            cachedTransform.position = backPosition;
        }
        else
        {
            // "Forward" phase: Smooth movement towards target with satisfying acceleration
            // Add extra smoothing for the transition from back to forward
            float smoothedProgress = easedProgress * easedProgress * (3f - 2f * easedProgress); // Smoothstep for extra satisfaction
            cachedTransform.position = Vector3.Lerp(attractionStartPosition, currentTarget, smoothedProgress);
        }
        
        // Check if we're close enough to collect (safety fallback)
        if (distanceToPlayer < 0.5f && !isBeingCollected && isInitialized)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SplinterPickup] Distance trigger activated - distance: {distanceToPlayer}");
            }
            
            CollectSplinter();
        }
    }
    
    private void ReturnToPool()
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[SplinterPickup] Returning to pool");
        }
        
        // Return to object pool
        PoolManager.Instance.Despawn(gameObject);
    }
    
    #endregion
    
    #region Debug Tools
    
    [Title("Debug Tools")]
    [Button("Test Red Splinter Pickup")]
    [PropertyOrder(100)]
    private void DebugTestRedPickup()
    {
        ConfigurePickup(SplinterType.Red, 1, transform.position);
    }
    
    [Button("Test Blue Splinter Pickup")]
    [PropertyOrder(101)]
    private void DebugTestBluePickup()
    {
        ConfigurePickup(SplinterType.Blue, 3, transform.position);
    }
    
    [Button("Force Collect")]
    [PropertyOrder(102)]
    [ShowIf("isInitialized")]
    private void DebugForceCollect()
    {
        CollectSplinter();
    }
    
    #endregion
    
    #region Gizmos
    
    private void OnDrawGizmosSelected()
    {
        // Draw magnetic range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, magneticRange);
        
        // Draw splinter type color
        if (currentSplinterType.IsSingleType())
        {
            Gizmos.color = currentSplinterType.GetSplinterColor();
            Gizmos.DrawSphere(transform.position, 0.2f);
        }
    }
    
    #endregion
}
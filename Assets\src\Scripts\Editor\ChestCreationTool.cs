using UnityEngine;
using UnityEditor;
using System.IO;

    public class ChestCreationTool : EditorWindow
    {
        private enum ChestType
        {
            BasicChest,
            TreasureChest,
            LockedChest,
            BossChest
        }
        
        private ChestType selectedChestType = ChestType.BasicChest;
        private string chestName = "NewChest";
        private Color chestColor = new Color(0.8f, 0.6f, 0.4f); // Brown color
        private float chestScale = 1f;
        
        // Interaction settings
        private float interactionRange = 1.5f;
        private bool requiresInteraction = true;
        private KeyCode interactionKey = KeyCode.E;
        private bool isReusable = false;
        
        // Animation settings
        private SpriteAnimation closedAnimation = null;
        private SpriteAnimation openingAnimation = null;
        private SpriteAnimation openedAnimation = null;
        
        
        // Audio settings
        private AudioClip openSound = null;
        private AudioClip lockedSound = null;
        
        // Creation settings
        private string prefabPath = "Assets/src/Prefabs/Chests/";
        private bool createInScene = true;
        private bool saveToPrefab = true;
        
        [MenuItem("2D Rogue/Objects/Chest Creation Tool")]
        public static void ShowWindow()
        {
            ChestCreationTool window = GetWindow<ChestCreationTool>("Chest Creation Tool");
            window.minSize = new Vector2(400, 600);
            window.Show();
        }
        
        private void OnGUI()
        {
            GUILayout.Label("Chest Creation Tool", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            // Chest Configuration
            GUILayout.Label("Chest Configuration", EditorStyles.boldLabel);
            selectedChestType = (ChestType)EditorGUILayout.EnumPopup("Chest Type", selectedChestType);
            chestName = EditorGUILayout.TextField("Chest Name", chestName);
            chestColor = EditorGUILayout.ColorField("Chest Color", chestColor);
            chestScale = EditorGUILayout.Slider("Chest Scale", chestScale, 0.5f, 3f);
            
            GUILayout.Space(10);
            
            // Interaction Settings
            GUILayout.Label("Interaction Settings", EditorStyles.boldLabel);
            interactionRange = EditorGUILayout.Slider("Interaction Range", interactionRange, 0.5f, 5f);
            requiresInteraction = EditorGUILayout.Toggle("Requires Interaction", requiresInteraction);
            
            if (requiresInteraction)
            {
                interactionKey = (KeyCode)EditorGUILayout.EnumPopup("Interaction Key", interactionKey);
            }
            
            isReusable = EditorGUILayout.Toggle("Is Reusable", isReusable);
            
            GUILayout.Space(10);
            
            // Animation Settings
            GUILayout.Label("Animation Settings", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("Assign SpriteAnimation assets for chest states. These replace the old string-based system.", MessageType.Info);
            
            closedAnimation = (SpriteAnimation)EditorGUILayout.ObjectField("Closed Animation", closedAnimation, typeof(SpriteAnimation), false);
            openingAnimation = (SpriteAnimation)EditorGUILayout.ObjectField("Opening Animation", openingAnimation, typeof(SpriteAnimation), false);
            openedAnimation = (SpriteAnimation)EditorGUILayout.ObjectField("Opened Animation", openedAnimation, typeof(SpriteAnimation), false);
            
            // Quick setup buttons
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Auto-Find Chest Animations", GUILayout.Height(20)))
            {
                AutoFindChestAnimations();
            }
            if (GUILayout.Button("Create Default Animations", GUILayout.Height(20)))
            {
                CreateDefaultChestAnimations();
            }
            EditorGUILayout.EndHorizontal();
            
            GUILayout.Space(10);
            
            
            // Audio Settings
            GUILayout.Label("Audio Settings", EditorStyles.boldLabel);
            openSound = (AudioClip)EditorGUILayout.ObjectField("Open Sound", openSound, typeof(AudioClip), false);
            lockedSound = (AudioClip)EditorGUILayout.ObjectField("Locked Sound", lockedSound, typeof(AudioClip), false);
            
            GUILayout.Space(10);
            
            // Creation Options
            GUILayout.Label("Creation Options", EditorStyles.boldLabel);
            createInScene = EditorGUILayout.Toggle("Create in Scene", createInScene);
            saveToPrefab = EditorGUILayout.Toggle("Save as Prefab", saveToPrefab);
            
            if (saveToPrefab)
            {
                EditorGUILayout.LabelField("Prefab Path:");
                prefabPath = EditorGUILayout.TextField(prefabPath);
                
                if (GUILayout.Button("Browse..."))
                {
                    string selectedPath = EditorUtility.SaveFolderPanel("Select Prefab Folder", prefabPath, "");
                    if (!string.IsNullOrEmpty(selectedPath))
                    {
                        // Convert absolute path to relative path
                        if (selectedPath.StartsWith(Application.dataPath))
                        {
                            prefabPath = "Assets" + selectedPath.Substring(Application.dataPath.Length);
                        }
                    }
                }
            }
            
            GUILayout.Space(20);
            
            // Create Button
            if (GUILayout.Button("Create Chest", GUILayout.Height(30)))
            {
                CreateChest();
            }
            
            GUILayout.Space(10);
            
            // Info Box
            EditorGUILayout.HelpBox(GetChestTypeDescription(), MessageType.Info);
        }
        
        private string GetChestTypeDescription()
        {
            switch (selectedChestType)
            {
                case ChestType.BasicChest:
                    return "Basic Chest: Standard loot container with simple interaction. Good for common areas.";
                case ChestType.TreasureChest:
                    return "Treasure Chest: Higher value loot with better gem drop rates. Suitable for hidden areas.";
                case ChestType.LockedChest:
                    return "Locked Chest: Requires key or special condition to open. Contains valuable loot.";
                case ChestType.BossChest:
                    return "Boss Chest: High-tier loot container for boss encounters. Guaranteed rare gems.";
                default:
                    return "";
            }
        }
        
        private void CreateChest()
        {
            // Apply chest type presets
            ApplyChestTypePresets();
            
            // Create the root GameObject
            GameObject chestGO = new GameObject(chestName);
            
            try
            {
                // Add SpriteRenderer
                SpriteRenderer spriteRenderer = chestGO.AddComponent<SpriteRenderer>();
                ConfigureSpriteRenderer(spriteRenderer);
                
                // Add SpatialCollider
                SpatialCollider spatialCollider = chestGO.AddComponent<SpatialCollider>();
                ConfigureSpatialCollider(spatialCollider);
                
                // Add SpriteAnimator
                SpriteAnimator spriteAnimator = chestGO.AddComponent<SpriteAnimator>();
                ConfigureSpriteAnimator(spriteAnimator);
                
                // Add ChestComponent
                ChestComponent chestComponent = chestGO.AddComponent<ChestComponent>();
                ConfigureChestComponent(chestComponent);
                
                
                // Create interaction prompt
                GameObject prompt = CreateInteractionPrompt(chestGO.transform);
                SetPrivateField(chestComponent, "interactionPrompt", prompt);
                
                // Configure transform
                chestGO.transform.localScale = Vector3.one * chestScale;
                
                // Save as prefab if requested
                GameObject finalObject = chestGO;
                if (saveToPrefab)
                {
                    finalObject = SaveAsPrefab(chestGO);
                }
                
                // Clean up scene object if not creating in scene
                if (!createInScene && saveToPrefab)
                {
                    DestroyImmediate(chestGO);
                }
                
                // Select the created object
                Selection.activeObject = finalObject;
                if (finalObject != null)
                {
                    EditorGUIUtility.PingObject(finalObject);
                }
                
                Debug.Log($"Chest '{chestName}' created successfully!");
                
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to create chest: {e.Message}");
                if (chestGO != null)
                {
                    DestroyImmediate(chestGO);
                }
            }
        }
        
        private void ApplyChestTypePresets()
        {
            switch (selectedChestType)
            {
                case ChestType.BasicChest:
                    chestColor = new Color(0.8f, 0.6f, 0.4f); // Brown
                    isReusable = false;
                    break;
                    
                case ChestType.TreasureChest:
                    chestColor = new Color(1f, 0.8f, 0.2f); // Gold
                    isReusable = false;
                    chestScale = 1.2f;
                    break;
                    
                case ChestType.LockedChest:
                    chestColor = new Color(0.6f, 0.6f, 0.6f); // Silver
                    isReusable = false;
                    break;
                    
                case ChestType.BossChest:
                    chestColor = new Color(0.4f, 0.2f, 0.8f); // Purple
                    isReusable = false;
                    chestScale = 1.5f;
                    break;
            }
        }
        
        private void ConfigureSpriteRenderer(SpriteRenderer spriteRenderer)
        {
            // Try to find a chest sprite
            Sprite chestSprite = FindChestSprite();
            
            if (chestSprite == null)
            {
                // Create a simple colored sprite
                chestSprite = CreateSimpleSprite();
            }
            
            spriteRenderer.sprite = chestSprite;
            spriteRenderer.color = chestColor;
            spriteRenderer.sortingLayerName = "Default";
            spriteRenderer.sortingOrder = 5;
        }
        
        private Sprite FindChestSprite()
        {
            // Try to find existing chest sprites
            string[] searchPaths = {
                "Assets/Sprites/chest.png",
                "Assets/Sprites/Chest/chest.png",
                "Assets/Sprites/Objects/chest.png"
            };
            
            foreach (string path in searchPaths)
            {
                Sprite sprite = AssetDatabase.LoadAssetAtPath<Sprite>(path);
                if (sprite != null)
                {
                    return sprite;
                }
            }
            
            // Try to find any sprite as fallback
            string[] guids = AssetDatabase.FindAssets("t:Sprite chest");
            if (guids.Length > 0)
            {
                string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                return AssetDatabase.LoadAssetAtPath<Sprite>(path);
            }
            
            return null;
        }
        
        private Sprite CreateSimpleSprite()
        {
            // Create a simple 32x32 colored square
            Texture2D texture = new Texture2D(32, 32);
            Color[] colors = new Color[32 * 32];
            
            for (int i = 0; i < colors.Length; i++)
            {
                colors[i] = chestColor;
            }
            
            texture.SetPixels(colors);
            texture.Apply();
            
            return Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f), 32);
        }
        
        private void ConfigureSpatialCollider(SpatialCollider spatialCollider)
        {
            spatialCollider.shape = ColliderShape.Circle;
            spatialCollider.radius = interactionRange;
            spatialCollider.SetLayer(CollisionLayers.Interactable);
            spatialCollider.SetTrigger(true);
        }
        
        private void ConfigureSpriteAnimator(SpriteAnimator spriteAnimator)
        {
            // SpriteAnimator will be configured with animations in the inspector
            // For now, just ensure it exists
        }
        
        private void ConfigureChestComponent(ChestComponent chestComponent)
        {
            // Use reflection to set private fields
            SetPrivateField(chestComponent, "interactionRange", interactionRange);
            SetPrivateField(chestComponent, "isReusable", isReusable);
            SetPrivateField(chestComponent, "closedAnimation", closedAnimation);
            SetPrivateField(chestComponent, "openingAnimation", openingAnimation);
            SetPrivateField(chestComponent, "openedAnimation", openedAnimation);
            SetPrivateField(chestComponent, "requiresInteraction", requiresInteraction);
            SetPrivateField(chestComponent, "interactionKey", interactionKey);
            SetPrivateField(chestComponent, "openSound", openSound);
            SetPrivateField(chestComponent, "lockedSound", lockedSound);
        }
        
        private GameObject CreateInteractionPrompt(Transform parent)
        {
            GameObject prompt = new GameObject("InteractionPrompt");
            prompt.transform.SetParent(parent);
            prompt.transform.localPosition = new Vector3(0, 1.5f, 0);
            
            // Add a simple text or sprite for the prompt
            SpriteRenderer promptRenderer = prompt.AddComponent<SpriteRenderer>();
            
            // Try to find an interaction prompt sprite
            Sprite promptSprite = AssetDatabase.LoadAssetAtPath<Sprite>("Assets/Sprites/UI/interaction_prompt.png");
            if (promptSprite == null)
            {
                // Create a simple colored circle
                Texture2D texture = new Texture2D(16, 16);
                Color[] colors = new Color[16 * 16];
                
                for (int i = 0; i < colors.Length; i++)
                {
                    colors[i] = Color.white;
                }
                
                texture.SetPixels(colors);
                texture.Apply();
                
                promptSprite = Sprite.Create(texture, new Rect(0, 0, 16, 16), new Vector2(0.5f, 0.5f), 16);
            }
            
            promptRenderer.sprite = promptSprite;
            promptRenderer.sortingOrder = 10;
            prompt.SetActive(false);
            
            return prompt;
        }
        
        private GameObject SaveAsPrefab(GameObject chestGO)
        {
            string fullPath = Path.Combine(prefabPath, chestName + ".prefab");
            
            // Ensure directory exists
            string directory = Path.GetDirectoryName(fullPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                AssetDatabase.Refresh();
            }
            
            // Save as prefab
            GameObject prefab = PrefabUtility.SaveAsPrefabAsset(chestGO, fullPath);
            
            Debug.Log($"Chest prefab saved to: {fullPath}");
            return prefab;
        }
        
        private void AutoFindChestAnimations()
        {
            // Search for chest animations in the project
            string[] guids = AssetDatabase.FindAssets("t:SpriteAnimation");
            
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                SpriteAnimation animation = AssetDatabase.LoadAssetAtPath<SpriteAnimation>(path);
                
                if (animation != null)
                {
                    string animName = animation.AnimationName.ToLower();
                    
                    if (animName.Contains("chest"))
                    {
                        if (animName.Contains("closed") && closedAnimation == null)
                            closedAnimation = animation;
                        else if (animName.Contains("opening") && openingAnimation == null)
                            openingAnimation = animation;
                        else if (animName.Contains("opened") && openedAnimation == null)
                            openedAnimation = animation;
                    }
                }
            }
            
            if (closedAnimation != null || openingAnimation != null || openedAnimation != null)
            {
                Debug.Log("Auto-found chest animations!");
            }
            else
            {
                EditorUtility.DisplayDialog("No Animations Found", 
                    "No chest animations found. Use 'Create Default Animations' to create them.", "OK");
            }
        }
        
        private void CreateDefaultChestAnimations()
        {
            // This would open the Sprite Animation Tool with chest-specific settings
            EditorUtility.DisplayDialog("Create Animations", 
                "Use the Sprite Animation Tool (2D Rogue → Animation → Sprite Animation Tool) to create:\n\n" +
                "• Chest_Closed (1 frame, Loop)\n" +
                "• Chest_Opening (4-8 frames, Once)\n" +
                "• Chest_Opened (1 frame, Loop)\n\n" +
                "Then assign them here.", "OK");
        }
        
        private void SetPrivateField(object obj, string fieldName, object value)
        {
            var type = obj.GetType();
            var field = type.GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (field == null)
            {
                field = type.GetField(fieldName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            }
            
            if (field != null)
            {
                field.SetValue(obj, value);
            }
            else
            {
                Debug.LogWarning($"Field '{fieldName}' not found in type '{type.Name}'");
            }
        }
    }

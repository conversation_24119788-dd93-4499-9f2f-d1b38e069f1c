using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Test script to verify autonomous support gem functionality
/// </summary>
public class AutonomousSupportGemTest : MonoBehaviour
{
    [Title("Test Configuration")]
    [SerializeField] private SupportGemData autonomousSupportGem;
    [SerializeField] private SkillGemData testSkillGem;
    [SerializeField] private Transform testEnemyTransform;

    [Title("Support Gem Compatibility Testing")]
    [SerializeField] private SupportGemData spellEchoGem;
    [SerializeField] private SupportGemData pierceGem;
    [SerializeField] private SupportGemData chainGem;
    [SerializeField] private SupportGemData forkGem;
    [SerializeField] private SupportGemData areaGem;
    [SerializeField] private SupportGemData multipleProjectilesGem;
    
    [Title("Test Results")]
    [ShowInInspector, ReadOnly] private bool hasAutonomousSupport;
    [ShowInInspector, ReadOnly] private float autonomousRange;
    [ShowInInspector, <PERSON>Only] private float updateInterval;
    [ShowInInspector, <PERSON>Only] private string tooltipText;
    
    // Cache for nearby enemies to eliminate allocations in collision detection
    private readonly System.Collections.Generic.List<ICollidable> _nearbyEnemiesCache = new System.Collections.Generic.List<ICollidable>(32);
    
    [Title("Runtime Testing")]
    [Button("Test Autonomous Support Detection")]
    private void TestAutonomousDetection()
    {
        if (autonomousSupportGem == null)
        {
            Debug.LogError("No autonomous support gem assigned!");
            return;
        }
        
        hasAutonomousSupport = autonomousSupportGem.addsAutonomous;
        autonomousRange = autonomousSupportGem.autonomousRange;
        updateInterval = autonomousSupportGem.autonomousUpdateInterval;
        
        Debug.Log($"Autonomous Support: {hasAutonomousSupport}");
        Debug.Log($"Range: {autonomousRange}");
        Debug.Log($"Update Interval: {updateInterval}");
    }
    
    [Button("Test Tooltip Display")]
    private void TestTooltipDisplay()
    {
        if (autonomousSupportGem == null)
        {
            Debug.LogError("No autonomous support gem assigned!");
            return;
        }
        
        tooltipText = autonomousSupportGem.GetTooltipText();
        Debug.Log($"Tooltip Text:\n{tooltipText}");
    }
    
    [Button("Test GemInstance Range")]
    private void TestGemInstanceRange()
    {
        if (autonomousSupportGem == null)
        {
            Debug.LogError("No autonomous support gem assigned!");
            return;
        }
        
        // Create a test gem instance
        var gemInstance = new GemInstance(autonomousSupportGem, GemRarity.Uncommon);
        float range = gemInstance.GetAutonomousRange();
        
        Debug.Log($"GemInstance Autonomous Range: {range}");
    }
    
    [Button("Test Enemy Detection")]
    private void TestEnemyDetection()
    {
        if (testEnemyTransform == null)
        {
            Debug.LogError("No test enemy transform assigned!");
            return;
        }
        
        if (CollisionManager.Instance == null)
        {
            Debug.LogError("CollisionManager not found!");
            return;
        }
        
        Vector2 playerPos = transform.position;
        float testRange = 10f;
        
        // Use NonAlloc method to avoid allocations and race conditions
        CollisionManager.Instance.GetCollidersInRadiusNonAlloc(
            playerPos, testRange, _nearbyEnemiesCache, CollisionLayers.Enemy);
        
        Debug.Log($"Found {_nearbyEnemiesCache.Count} enemies within {testRange} units");
        
        foreach (var enemy in _nearbyEnemiesCache)
        {
            float distance = Vector2.Distance(playerPos, enemy.Position);
            Debug.Log($"Enemy at distance: {distance:F2}");
        }
    }
    
    [Button("Test GemSocketController Integration")]
    private void TestGemSocketControllerIntegration()
    {
        if (autonomousSupportGem == null || testSkillGem == null)
        {
            Debug.LogError("Missing gem assignments!");
            return;
        }

        // Create test gem instances
        var skillInstance = new GemInstance(testSkillGem, GemRarity.Common);
        var supportInstance = new GemInstance(autonomousSupportGem, GemRarity.Uncommon);

        // Create a test controller (this would normally be done by the equipment system)
        var controller = new GemSocketController();
        controller.skillGemInstance = skillInstance;
        controller.supportGemInstances.Add(supportInstance);

        // Test autonomous detection
        bool hasAutonomous = controller.HasAutonomous();
        float range = controller.GetAutonomousRange();
        float interval = controller.GetAutonomousUpdateInterval();

        Debug.Log($"GemSocketController Test Results:");
        Debug.Log($"- Has Autonomous: {hasAutonomous}");
        Debug.Log($"- Range: {range}");
        Debug.Log($"- Update Interval: {interval}");
        Debug.Log("This verifies the autonomous support gem detection works correctly");
    }

    [Button("Test Manual Casting Block")]
    private void TestManualCastingBlock()
    {
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        if (skillExecutor == null)
        {
            Debug.LogError("SkillExecutor not found in scene!");
            return;
        }

        Debug.Log("Testing manual casting block functionality:");
        Debug.Log("1. Equip a skill with autonomous support gem");
        Debug.Log("2. Try to manually cast the skill (should be blocked)");
        Debug.Log("3. Check console for blocking message");
        Debug.Log("4. Verify autonomous casting still works when enemies are in range");
    }

    [Button("Debug Autonomous System Status")]
    private void DebugAutonomousSystemStatus()
    {
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        if (skillExecutor == null)
        {
            Debug.LogError("SkillExecutor not found in scene!");
            return;
        }

        Debug.Log("=== Autonomous System Debug ===");
        Debug.Log("1. Enable debug logging in SkillExecutor inspector");
        Debug.Log("2. Equip a skill with autonomous support gem");
        Debug.Log("3. Watch console for autonomous system messages:");
        Debug.Log("   - 'Started autonomous casting for skill in slot X'");
        Debug.Log("   - 'Processing X autonomous skills' (every 5 seconds)");
        Debug.Log("   - 'No enemies in range' or 'Autonomous casting at enemy'");
        Debug.Log("4. Place enemies within range to test auto-casting");
        Debug.Log("5. Check that manual casting is blocked but autonomous setup occurs");
    }

    [Button("Test Support Gem Compatibility")]
    private void TestSupportGemCompatibility()
    {
        Debug.Log("=== Support Gem Compatibility Test ===");

        if (testSkillGem == null || autonomousSupportGem == null)
        {
            Debug.LogError("Missing required gems for compatibility test!");
            return;
        }

        Debug.Log("Testing autonomous support gem compatibility with other support gems:");
        Debug.Log("1. SPELL ECHO + AUTONOMOUS:");
        TestGemCombination("Spell Echo", spellEchoGem, autonomousSupportGem);

        Debug.Log("2. PIERCE + AUTONOMOUS:");
        TestGemCombination("Pierce", pierceGem, autonomousSupportGem);

        Debug.Log("3. CHAIN + AUTONOMOUS:");
        TestGemCombination("Chain", chainGem, autonomousSupportGem);

        Debug.Log("4. FORK + AUTONOMOUS:");
        TestGemCombination("Fork", forkGem, autonomousSupportGem);

        Debug.Log("5. AREA + AUTONOMOUS:");
        TestGemCombination("Area", areaGem, autonomousSupportGem);

        Debug.Log("6. MULTIPLE PROJECTILES + AUTONOMOUS:");
        TestGemCombination("Multiple Projectiles", multipleProjectilesGem, autonomousSupportGem);
    }

    private void TestGemCombination(string gemType, SupportGemData supportGem, SupportGemData autonomousGem)
    {
        if (supportGem == null)
        {
            Debug.LogWarning($"   {gemType} gem not assigned - skipping test");
            return;
        }

        // Create test gem instances
        var skillInstance = new GemInstance(testSkillGem, GemRarity.Common);
        var supportInstance = new GemInstance(supportGem, GemRarity.Uncommon);
        var autonomousInstance = new GemInstance(autonomousGem, GemRarity.Uncommon);

        // Create test controller
        var controller = new GemSocketController();
        controller.skillGemInstance = skillInstance;
        controller.supportGemInstances.Add(supportInstance);
        controller.supportGemInstances.Add(autonomousInstance);

        // Test compatibility
        bool hasAutonomous = controller.HasAutonomous();
        bool hasOtherSupport = CheckForSupportType(controller, gemType);

        Debug.Log($"   {gemType} + Autonomous: Autonomous={hasAutonomous}, {gemType}={hasOtherSupport}");

        if (hasAutonomous && hasOtherSupport)
        {
            Debug.Log($"   ✅ {gemType} + Autonomous combination detected successfully");
        }
        else
        {
            Debug.LogWarning($"   ⚠️ {gemType} + Autonomous combination may have issues");
        }
    }

    private bool CheckForSupportType(GemSocketController controller, string supportType)
    {
        switch (supportType)
        {
            case "Spell Echo":
                return controller.HasSpellEcho();
            case "Pierce":
                return controller.HasPierce();
            case "Chain":
                return controller.HasChain();
            case "Fork":
                return controller.HasFork();
            case "Area":
                return controller.HasAreaDamage();
            case "Multiple Projectiles":
                return controller.HasMultipleProjectiles();
            default:
                return false;
        }
    }

    [Button("Test Autonomous Spell Echo Integration")]
    private void TestAutonomousSpellEchoIntegration()
    {
        Debug.Log("=== Autonomous + Spell Echo Integration Test ===");

        if (spellEchoGem == null || autonomousSupportGem == null)
        {
            Debug.LogError("Missing Spell Echo or Autonomous gems!");
            return;
        }

        Debug.Log("Testing specific Spell Echo + Autonomous integration:");
        Debug.Log("1. Equip a SPELL skill with both Spell Echo and Autonomous support gems");
        Debug.Log("2. Place enemies within autonomous range");
        Debug.Log("3. Expected behavior:");
        Debug.Log("   - Skill auto-casts at enemy (autonomous)");
        Debug.Log("   - Spell echo triggers additional casts at same target position");
        Debug.Log("   - Echo casts should use enemy position, not mouse position");
        Debug.Log("4. Check console for 'Started autonomous spell echo' messages");
        Debug.Log("5. Verify echo spread radius works around enemy position");

        // Test gem data
        Debug.Log($"Spell Echo Data: Echoes={spellEchoGem.echoCount}, Delay={spellEchoGem.echoDelay}s, Spread={spellEchoGem.echoSpreadRadius}");
        Debug.Log($"Autonomous Data: Range={autonomousSupportGem.autonomousRange}, Interval={autonomousSupportGem.autonomousUpdateInterval}s");
    }
    
    private void OnDrawGizmosSelected()
    {
        if (autonomousSupportGem != null && autonomousSupportGem.addsAutonomous)
        {
            // Draw the autonomous range
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, autonomousSupportGem.autonomousRange);
            
            // Draw line to test enemy if assigned
            if (testEnemyTransform != null)
            {
                float distance = Vector3.Distance(transform.position, testEnemyTransform.position);
                Gizmos.color = distance <= autonomousSupportGem.autonomousRange ? Color.green : Color.red;
                Gizmos.DrawLine(transform.position, testEnemyTransform.position);
            }
        }
    }
}

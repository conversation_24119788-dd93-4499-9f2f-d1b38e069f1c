using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Automatically manages Collider2D components for SpatialCollider.
/// Adds appropriate Collider2D components based on shape settings and keeps them synchronized.
/// </summary>
public static class Collider2DAutoManager
{
    /// <summary>
    /// Automatically add or update Collider2D component based on SpatialCollider settings
    /// </summary>
    public static void EnsureCollider2D(SpatialCollider spatialCollider, Physics2DLayerMapping layerMapping = null)
    {
        if (spatialCollider == null) return;
        
        GameObject gameObject = spatialCollider.gameObject;
        
        // Remove existing Collider2D components that don't match current shape
        RemoveIncompatibleColliders(spatialCollider);
        
        // Add or update the correct Collider2D component
        switch (spatialCollider.Shape)
        {
            case CollisionShape.Circle:
                EnsureCircleCollider2D(spatialCollider);
                break;
                
            case CollisionShape.Box:
                EnsureBoxCollider2D(spatialCollider);
                break;
        }
        
        // Set layer if mapping is provided
        if (layerMapping != null)
        {
            int unityLayer = layerMapping.GetUnityLayer(spatialCollider.Layer);
            if (unityLayer != -1)
            {
                gameObject.layer = unityLayer;
            }
        }
    }
    
    /// <summary>
    /// Remove Collider2D components that don't match the current SpatialCollider shape
    /// </summary>
    private static void RemoveIncompatibleColliders(SpatialCollider spatialCollider)
    {
        GameObject gameObject = spatialCollider.gameObject;
        
        switch (spatialCollider.Shape)
        {
            case CollisionShape.Circle:
                // Remove BoxCollider2D if present
                var boxCollider = gameObject.GetComponent<BoxCollider2D>();
                if (boxCollider != null)
                {
                    if (Application.isPlaying)
                        Object.Destroy(boxCollider);
                    else
                        Object.DestroyImmediate(boxCollider);
                }
                break;
                
            case CollisionShape.Box:
                // Remove CircleCollider2D if present
                var circleCollider = gameObject.GetComponent<CircleCollider2D>();
                if (circleCollider != null)
                {
                    if (Application.isPlaying)
                        Object.Destroy(circleCollider);
                    else
                        Object.DestroyImmediate(circleCollider);
                }
                break;
        }
    }
    
    /// <summary>
    /// Ensure CircleCollider2D is present and configured correctly
    /// </summary>
    private static void EnsureCircleCollider2D(SpatialCollider spatialCollider)
    {
        GameObject gameObject = spatialCollider.gameObject;
        CircleCollider2D circleCollider = gameObject.GetComponent<CircleCollider2D>();
        
        if (circleCollider == null)
        {
            circleCollider = gameObject.AddComponent<CircleCollider2D>();
        }
        
        // Sync properties
        circleCollider.radius = spatialCollider.Radius;
        circleCollider.offset = spatialCollider.Offset;
        circleCollider.isTrigger = spatialCollider.IsTrigger;
    }
    
    /// <summary>
    /// Ensure BoxCollider2D is present and configured correctly
    /// </summary>
    private static void EnsureBoxCollider2D(SpatialCollider spatialCollider)
    {
        GameObject gameObject = spatialCollider.gameObject;
        BoxCollider2D boxCollider = gameObject.GetComponent<BoxCollider2D>();
        
        if (boxCollider == null)
        {
            boxCollider = gameObject.AddComponent<BoxCollider2D>();
        }
        
        // Sync properties
        boxCollider.size = spatialCollider.Size;
        boxCollider.offset = spatialCollider.Offset;
        boxCollider.isTrigger = spatialCollider.IsTrigger;
    }
    
    /// <summary>
    /// Synchronize Collider2D properties with SpatialCollider settings
    /// </summary>
    public static void SynchronizeCollider2D(SpatialCollider spatialCollider)
    {
        if (spatialCollider == null) return;
        
        GameObject gameObject = spatialCollider.gameObject;
        
        switch (spatialCollider.Shape)
        {
            case CollisionShape.Circle:
                var circleCollider = gameObject.GetComponent<CircleCollider2D>();
                if (circleCollider != null)
                {
                    circleCollider.radius = spatialCollider.Radius;
                    circleCollider.offset = spatialCollider.Offset;
                    circleCollider.isTrigger = spatialCollider.IsTrigger;
                }
                break;
                
            case CollisionShape.Box:
                var boxCollider = gameObject.GetComponent<BoxCollider2D>();
                if (boxCollider != null)
                {
                    boxCollider.size = spatialCollider.Size;
                    boxCollider.offset = spatialCollider.Offset;
                    boxCollider.isTrigger = spatialCollider.IsTrigger;
                }
                break;
        }
    }
    
    /// <summary>
    /// Check if the current Collider2D matches the SpatialCollider configuration
    /// </summary>
    public static bool IsCollider2DSynchronized(SpatialCollider spatialCollider)
    {
        if (spatialCollider == null) return false;
        
        GameObject gameObject = spatialCollider.gameObject;
        
        switch (spatialCollider.Shape)
        {
            case CollisionShape.Circle:
                var circleCollider = gameObject.GetComponent<CircleCollider2D>();
                if (circleCollider == null) return false;
                
                return Mathf.Approximately(circleCollider.radius, spatialCollider.Radius) &&
                       circleCollider.offset == spatialCollider.Offset &&
                       circleCollider.isTrigger == spatialCollider.IsTrigger;
                
            case CollisionShape.Box:
                var boxCollider = gameObject.GetComponent<BoxCollider2D>();
                if (boxCollider == null) return false;
                
                return boxCollider.size == spatialCollider.Size &&
                       boxCollider.offset == spatialCollider.Offset &&
                       boxCollider.isTrigger == spatialCollider.IsTrigger;
        }
        
        return false;
    }
    
    /// <summary>
    /// Remove all Collider2D components from the GameObject
    /// </summary>
    public static void RemoveAllCollider2D(GameObject gameObject)
    {
        var colliders = gameObject.GetComponents<Collider2D>();
        foreach (var collider in colliders)
        {
            if (Application.isPlaying)
                Object.Destroy(collider);
            else
                Object.DestroyImmediate(collider);
        }
    }
    
    /// <summary>
    /// Validate that the GameObject has the correct Collider2D for its SpatialCollider
    /// </summary>
    public static ColliderValidationResult ValidateCollider2D(SpatialCollider spatialCollider)
    {
        if (spatialCollider == null)
            return new ColliderValidationResult(false, "SpatialCollider is null");
        
        GameObject gameObject = spatialCollider.gameObject;
        
        switch (spatialCollider.Shape)
        {
            case CollisionShape.Circle:
                var circleCollider = gameObject.GetComponent<CircleCollider2D>();
                var boxCollider = gameObject.GetComponent<BoxCollider2D>();
                
                if (circleCollider == null)
                    return new ColliderValidationResult(false, "Missing CircleCollider2D for Circle shape");
                
                if (boxCollider != null)
                    return new ColliderValidationResult(false, "BoxCollider2D present but shape is Circle");
                
                if (!IsCollider2DSynchronized(spatialCollider))
                    return new ColliderValidationResult(false, "CircleCollider2D properties not synchronized");
                
                break;
                
            case CollisionShape.Box:
                boxCollider = gameObject.GetComponent<BoxCollider2D>();
                circleCollider = gameObject.GetComponent<CircleCollider2D>();
                
                if (boxCollider == null)
                    return new ColliderValidationResult(false, "Missing BoxCollider2D for Box shape");
                
                if (circleCollider != null)
                    return new ColliderValidationResult(false, "CircleCollider2D present but shape is Box");
                
                if (!IsCollider2DSynchronized(spatialCollider))
                    return new ColliderValidationResult(false, "BoxCollider2D properties not synchronized");
                
                break;
        }
        
        return new ColliderValidationResult(true, "Collider2D is valid and synchronized");
    }
    
    public struct ColliderValidationResult
    {
        public bool IsValid;
        public string Message;
        
        public ColliderValidationResult(bool isValid, string message)
        {
            IsValid = isValid;
            Message = message;
        }
    }
}

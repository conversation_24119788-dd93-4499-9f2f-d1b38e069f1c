using UnityEngine;
using UnityEditor;

using System.IO;

namespace RogueLike.Editor
{
    public static class QuickCreateChests
    {
        private const string PREFAB_PATH = "Assets/src/Prefabs/Chests/";
        
        [MenuItem("2D Rogue/Objects/Quick Create/Basic Chest")]
        public static void CreateBasicChest()
        {
            CreateChest("BasicChest", new Color(0.8f, 0.6f, 0.4f), 1f);
        }
        
        [MenuItem("2D Rogue/Objects/Quick Create/Treasure Chest")]
        public static void CreateTreasureChest()
        {
            CreateChest("TreasureChest", new Color(1f, 0.8f, 0.2f), 1.2f);
        }
        
        [MenuItem("2D Rogue/Objects/Quick Create/Locked Chest")]
        public static void CreateLockedChest()
        {
            CreateChest("LockedChest", new Color(0.6f, 0.6f, 0.6f), 1f);
        }
        
        [MenuItem("2D Rogue/Objects/Quick Create/Boss Chest")]
        public static void CreateBossChest()
        {
            CreateChest("BossChest", new Color(0.4f, 0.2f, 0.8f), 1.5f);
        }
        
        [MenuItem("2D Rogue/Objects/Quick Create/Create All Chest Types")]
        public static void CreateAllChestTypes()
        {
            CreateBasicChest();
            CreateTreasureChest();
            CreateLockedChest();
            CreateBossChest();
            
            Debug.Log("All chest types created successfully!");
        }
        
        [MenuItem("2D Rogue/Objects/Open Chests Folder")]
        public static void OpenChestsFolder()
        {
            // Ensure directory exists
            if (!Directory.Exists(PREFAB_PATH))
            {
                Directory.CreateDirectory(PREFAB_PATH);
                AssetDatabase.Refresh();
            }
            
            // Select the folder in Project window
            Object folderAsset = AssetDatabase.LoadAssetAtPath(PREFAB_PATH, typeof(Object));
            if (folderAsset != null)
            {
                Selection.activeObject = folderAsset;
                EditorGUIUtility.PingObject(folderAsset);
            }
        }
        
        private static GameObject CreateChest(string name, Color color, float scale)
        {
            // Create the root GameObject
            GameObject chestGO = new GameObject(name);
            
            try
            {
                // Add SpriteRenderer
                SpriteRenderer spriteRenderer = chestGO.AddComponent<SpriteRenderer>();
                ConfigureSpriteRenderer(spriteRenderer, color);
                
                // Add SpatialCollider
                SpatialCollider spatialCollider = chestGO.AddComponent<SpatialCollider>();
                ConfigureSpatialCollider(spatialCollider);
                
                // Add SpriteAnimator
                SpriteAnimator spriteAnimator = chestGO.AddComponent<SpriteAnimator>();
                
                // Add ChestComponent
                ChestComponent chestComponent = chestGO.AddComponent<ChestComponent>();
                ConfigureChestComponent(chestComponent);
                
                // Create interaction prompt
                GameObject prompt = CreateInteractionPrompt(chestGO.transform);
                SetPrivateField(chestComponent, "interactionPrompt", prompt);
                
                // Configure transform
                chestGO.transform.localScale = Vector3.one * scale;
                
                // Save as prefab
                GameObject prefab = SaveAsPrefab(chestGO, name);
                
                // Clean up scene object
                Object.DestroyImmediate(chestGO);
                
                // Select the created prefab
                Selection.activeObject = prefab;
                EditorGUIUtility.PingObject(prefab);
                
                Debug.Log($"Chest '{name}' created successfully at {PREFAB_PATH}{name}.prefab");
                return prefab;
                
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to create chest '{name}': {e.Message}");
                if (chestGO != null)
                {
                    Object.DestroyImmediate(chestGO);
                }
                return null;
            }
        }
        
        private static void ConfigureSpriteRenderer(SpriteRenderer spriteRenderer, Color color)
        {
            // Try to find a chest sprite
            Sprite chestSprite = FindChestSprite();
            
            if (chestSprite == null)
            {
                // Create a simple colored sprite
                chestSprite = CreateSimpleSprite(color);
            }
            
            spriteRenderer.sprite = chestSprite;
            spriteRenderer.color = color;
            spriteRenderer.sortingLayerName = "Default";
            spriteRenderer.sortingOrder = 5;
        }
        
        private static Sprite FindChestSprite()
        {
            // Try to find existing chest sprites
            string[] searchPaths = {
                "Assets/Sprites/chest.png",
                "Assets/Sprites/Chest/chest.png",
                "Assets/Sprites/Objects/chest.png"
            };
            
            foreach (string path in searchPaths)
            {
                Sprite sprite = AssetDatabase.LoadAssetAtPath<Sprite>(path);
                if (sprite != null)
                {
                    return sprite;
                }
            }
            
            // Try to find any sprite as fallback
            string[] guids = AssetDatabase.FindAssets("t:Sprite");
            if (guids.Length > 0)
            {
                string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                return AssetDatabase.LoadAssetAtPath<Sprite>(path);
            }
            
            return null;
        }
        
        private static Sprite CreateSimpleSprite(Color color)
        {
            // Create a simple 32x32 colored square
            Texture2D texture = new Texture2D(32, 32);
            Color[] colors = new Color[32 * 32];
            
            for (int i = 0; i < colors.Length; i++)
            {
                colors[i] = color;
            }
            
            texture.SetPixels(colors);
            texture.Apply();
            
            return Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f), 32);
        }
        
        private static void ConfigureSpatialCollider(SpatialCollider spatialCollider)
        {
            spatialCollider.shape = ColliderShape.Circle;
            spatialCollider.radius = 1.5f;
            spatialCollider.SetLayer(CollisionLayers.Interactable);
            spatialCollider.SetTrigger(true);
        }
        
        private static void ConfigureChestComponent(ChestComponent chestComponent)
        {
            // Set default values
            SetPrivateField(chestComponent, "interactionRange", 1.5f);
            SetPrivateField(chestComponent, "isReusable", false);
            
            // Try to find default chest animations
            var animations = FindChestAnimations();
            SetPrivateField(chestComponent, "closedAnimation", animations.closed);
            SetPrivateField(chestComponent, "openingAnimation", animations.opening);
            SetPrivateField(chestComponent, "openedAnimation", animations.opened);
            
            SetPrivateField(chestComponent, "requiresInteraction", true);
            SetPrivateField(chestComponent, "interactionKey", KeyCode.E);
            
            // Log warning if animations not found
            if (animations.closed == null || animations.opening == null || animations.opened == null)
            {
                Debug.LogWarning("ChestComponent: Some chest animations not found. Please assign them manually in the inspector.");
            }
        }
        
        private static GameObject CreateInteractionPrompt(Transform parent)
        {
            GameObject prompt = new GameObject("InteractionPrompt");
            prompt.transform.SetParent(parent);
            prompt.transform.localPosition = new Vector3(0, 1.5f, 0);
            
            // Add a simple sprite for the prompt
            SpriteRenderer promptRenderer = prompt.AddComponent<SpriteRenderer>();
            
            // Try to find an interaction prompt sprite
            Sprite promptSprite = AssetDatabase.LoadAssetAtPath<Sprite>("Assets/Sprites/UI/interaction_prompt.png");
            if (promptSprite == null)
            {
                // Create a simple white circle
                Texture2D texture = new Texture2D(16, 16);
                Color[] colors = new Color[16 * 16];
                
                for (int i = 0; i < colors.Length; i++)
                {
                    colors[i] = Color.white;
                }
                
                texture.SetPixels(colors);
                texture.Apply();
                
                promptSprite = Sprite.Create(texture, new Rect(0, 0, 16, 16), new Vector2(0.5f, 0.5f), 16);
            }
            
            promptRenderer.sprite = promptSprite;
            promptRenderer.sortingOrder = 10;
            prompt.SetActive(false);
            
            return prompt;
        }
        
        private static GameObject SaveAsPrefab(GameObject chestGO, string name)
        {
            string fullPath = Path.Combine(PREFAB_PATH, name + ".prefab");
            
            // Ensure directory exists
            string directory = Path.GetDirectoryName(fullPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                AssetDatabase.Refresh();
            }
            
            // Save as prefab
            return PrefabUtility.SaveAsPrefabAsset(chestGO, fullPath);
        }
        
        private static (SpriteAnimation closed, SpriteAnimation opening, SpriteAnimation opened) FindChestAnimations()
        {
            SpriteAnimation closed = null, opening = null, opened = null;
            
            // Search for chest animations in the project
            string[] guids = AssetDatabase.FindAssets("t:SpriteAnimation");
            
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                SpriteAnimation animation = AssetDatabase.LoadAssetAtPath<SpriteAnimation>(path);
                
                if (animation != null)
                {
                    string animName = animation.AnimationName.ToLower();
                    
                    if (animName.Contains("chest"))
                    {
                        if (animName.Contains("closed") && closed == null)
                            closed = animation;
                        else if (animName.Contains("opening") && opening == null)
                            opening = animation;
                        else if (animName.Contains("opened") && opened == null)
                            opened = animation;
                    }
                }
            }
            
            return (closed, opening, opened);
        }
        
        private static void SetPrivateField(object obj, string fieldName, object value)
        {
            var type = obj.GetType();
            var field = type.GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (field == null)
            {
                field = type.GetField(fieldName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            }
            
            if (field != null)
            {
                field.SetValue(obj, value);
            }
            else
            {
                Debug.LogWarning($"Field '{fieldName}' not found in type '{type.Name}'");
            }
        }
    }
}
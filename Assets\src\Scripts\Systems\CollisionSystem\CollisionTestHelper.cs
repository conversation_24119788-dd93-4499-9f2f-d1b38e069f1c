using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Helper script for testing collision detection in both Custom and Physics2D modes.
/// Provides runtime tools for switching modes and testing collision scenarios.
/// </summary>
public class CollisionTestHelper : MonoBehaviour
{
    [Title("Collision Mode Testing")]
    [SerializeField] private CollisionDetectionMode testMode = CollisionDetectionMode.Custom;
    
    [Title("Test Objects")]
    [SerializeField] private GameObject testProjectilePrefab;
    [SerializeField] private GameObject testEnemyPrefab;
    [SerializeField] private GameObject testPickupPrefab;
    
    [Title("Test Settings")]
    [SerializeField] private Vector2 spawnOffset = Vector2.right * 2f;
    [SerializeField] private bool enableTestLogging = true;
    
    [ShowInInspector, ReadOnly] private CollisionDetectionMode currentMode;
    [ShowInInspector, ReadOnly] private int testObjectsSpawned = 0;
    
    private void Start()
    {
        if (CollisionManager.Instance != null)
        {
            currentMode = CollisionManager.Instance.GetCollisionMode();
        }
    }
    
    [Button("Switch to Custom Mode")]
    public void SwitchToCustomMode()
    {
        SwitchCollisionMode(CollisionDetectionMode.Custom);
    }
    
    [Button("Switch to Physics2D Mode")]
    public void SwitchToPhysics2DMode()
    {
        SwitchCollisionMode(CollisionDetectionMode.Physics2D);
    }
    
    [Button("Toggle Collision Mode")]
    public void ToggleCollisionMode()
    {
        var newMode = currentMode == CollisionDetectionMode.Custom 
            ? CollisionDetectionMode.Physics2D 
            : CollisionDetectionMode.Custom;
        SwitchCollisionMode(newMode);
    }
    
    private void SwitchCollisionMode(CollisionDetectionMode mode)
    {
        if (CollisionManager.Instance == null)
        {
            Debug.LogError("[CollisionTestHelper] CollisionManager.Instance is null!");
            return;
        }
        
        // Use reflection to set the collision mode
        var collisionManagerType = typeof(CollisionManager);
        var collisionModeField = collisionManagerType.GetField("collisionMode", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (collisionModeField != null)
        {
            collisionModeField.SetValue(CollisionManager.Instance, mode);
            currentMode = mode;
            
            if (enableTestLogging)
            {
                Debug.Log($"[CollisionTestHelper] Switched collision mode to: {mode}");
            }
            
            // Reinitialize Physics2D system if switching to Physics2D mode
            if (mode == CollisionDetectionMode.Physics2D)
            {
                InitializePhysics2DMode();
            }
        }
        else
        {
            Debug.LogError("[CollisionTestHelper] Could not access collisionMode field!");
        }
    }
    
    private void InitializePhysics2DMode()
    {
        // Trigger Physics2D system initialization
        var initMethod = typeof(CollisionManager).GetMethod("InitializePhysics2DSystem", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (initMethod != null)
        {
            initMethod.Invoke(CollisionManager.Instance, null);
        }
        
        // Ensure all existing SpatialColliders have Collider2D components
        var spatialColliders = FindObjectsOfType<SpatialCollider>();
        foreach (var spatialCollider in spatialColliders)
        {
            var ensureMethod = typeof(SpatialCollider).GetMethod("EnsureCollider2DComponents", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (ensureMethod != null)
            {
                ensureMethod.Invoke(spatialCollider, null);
            }
        }
        
        if (enableTestLogging)
        {
            Debug.Log($"[CollisionTestHelper] Initialized Physics2D mode for {spatialColliders.Length} SpatialColliders");
        }
    }
    
    [Button("Spawn Test Projectile")]
    public void SpawnTestProjectile()
    {
        if (testProjectilePrefab == null)
        {
            Debug.LogWarning("[CollisionTestHelper] Test projectile prefab not assigned!");
            return;
        }
        
        Vector3 spawnPos = transform.position + (Vector3)spawnOffset;
        var projectile = Instantiate(testProjectilePrefab, spawnPos, Quaternion.identity);
        testObjectsSpawned++;
        
        if (enableTestLogging)
        {
            Debug.Log($"[CollisionTestHelper] Spawned test projectile at {spawnPos} (Mode: {currentMode})");
        }
    }
    
    [Button("Spawn Test Enemy")]
    public void SpawnTestEnemy()
    {
        if (testEnemyPrefab == null)
        {
            Debug.LogWarning("[CollisionTestHelper] Test enemy prefab not assigned!");
            return;
        }
        
        Vector3 spawnPos = transform.position - (Vector3)spawnOffset;
        var enemy = Instantiate(testEnemyPrefab, spawnPos, Quaternion.identity);
        testObjectsSpawned++;
        
        if (enableTestLogging)
        {
            Debug.Log($"[CollisionTestHelper] Spawned test enemy at {spawnPos} (Mode: {currentMode})");
        }
    }
    
    [Button("Spawn Test Pickup")]
    public void SpawnTestPickup()
    {
        if (testPickupPrefab == null)
        {
            Debug.LogWarning("[CollisionTestHelper] Test pickup prefab not assigned!");
            return;
        }
        
        Vector3 spawnPos = transform.position + Vector3.up * 2f;
        var pickup = Instantiate(testPickupPrefab, spawnPos, Quaternion.identity);
        testObjectsSpawned++;
        
        if (enableTestLogging)
        {
            Debug.Log($"[CollisionTestHelper] Spawned test pickup at {spawnPos} (Mode: {currentMode})");
        }
    }
    
    [Button("Clear Test Objects")]
    public void ClearTestObjects()
    {
        // Find and destroy test objects (assuming they have specific tags or components)
        var projectiles = FindObjectsOfType<Projectile>();
        var enemies = FindObjectsOfType<BaseEnemy>();
        var pickups = FindObjectsOfType<SplinterPickup>();
        
        int destroyed = 0;
        
        foreach (var projectile in projectiles)
        {
            if (projectile.name.Contains("Test") || projectile.name.Contains("(Clone)"))
            {
                DestroyImmediate(projectile.gameObject);
                destroyed++;
            }
        }
        
        foreach (var enemy in enemies)
        {
            if (enemy.name.Contains("Test") || enemy.name.Contains("(Clone)"))
            {
                DestroyImmediate(enemy.gameObject);
                destroyed++;
            }
        }
        
        foreach (var pickup in pickups)
        {
            if (pickup.name.Contains("Test") || pickup.name.Contains("(Clone)"))
            {
                DestroyImmediate(pickup.gameObject);
                destroyed++;
            }
        }
        
        testObjectsSpawned = 0;
        
        if (enableTestLogging)
        {
            Debug.Log($"[CollisionTestHelper] Cleared {destroyed} test objects");
        }
    }
    
    [Button("Test Collision Detection Performance")]
    public void TestCollisionPerformance()
    {
        if (CollisionManager.Instance == null) return;
        
        var spatialColliders = FindObjectsOfType<SpatialCollider>();
        
        Debug.Log($"[CollisionTestHelper] Performance test with {spatialColliders.Length} SpatialColliders in {currentMode} mode");
        
        // Test collision detection performance
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        int collisionChecks = 0;
        foreach (var collider in spatialColliders)
        {
            if (collider.IsActive)
            {
                // Simulate collision checks
                CollisionManager.Instance.UpdateCollidablePosition(collider);
                collisionChecks++;
            }
        }
        
        stopwatch.Stop();
        
        Debug.Log($"[CollisionTestHelper] Performed {collisionChecks} collision updates in {stopwatch.ElapsedMilliseconds}ms " +
                 $"({stopwatch.ElapsedTicks} ticks) - Mode: {currentMode}");
    }
    
    [Button("Validate Zero-GC Compliance")]
    public void ValidateZeroGC()
    {
        if (CollisionManager.Instance != null)
        {
            var validateMethod = typeof(CollisionManager).GetMethod("ValidateZeroGCCompliance", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (validateMethod != null)
            {
                validateMethod.Invoke(CollisionManager.Instance, null);
            }
        }
    }
    
    private void OnDrawGizmos()
    {
        // Draw spawn positions
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(transform.position + (Vector3)spawnOffset, 0.5f); // Projectile spawn
        
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position - (Vector3)spawnOffset, 0.5f); // Enemy spawn
        
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position + Vector3.up * 2f, 0.5f); // Pickup spawn
    }
}

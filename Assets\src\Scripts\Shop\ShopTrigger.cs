using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// ShopTrigger handles player interaction with shop entities in the world.
/// Uses SpatialCollider for detection and manages shop UI interaction.
/// Poolable component that can be spawned and despawned by ChunkContentSpawner.
/// </summary>
public class ShopTrigger : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, ISpatialCollisionHandler, ISpawnable
{
    [Title("Shop Configuration")]
    [Required]
    [SerializeField]
    [Tooltip("The shop that will be opened when player interacts")]
    private BaseShop shop;
    
    [Title("Interaction Settings")]
    [SerializeField]
    [Tooltip("Key to interact with the shop")]
    private KeyCode interactionKey = KeyCode.E;
    
    [SerializeField]
    [Tooltip("Alternative interaction key")]
    private KeyCode alternativeKey = KeyCode.F;
    
    [SerializeField]
    [Range(0.5f, 10f)]
    [Tooltip("Interaction range around the shop")]
    private float interactionRange = 2f;
    
    [SerializeField]
    [Tooltip("UI text to display when player can interact")]
    private string interactionPrompt = "Press {0} to open {1}";
    
    [Title("Visual Settings")]
    [SerializeField]
    [Tooltip("Sprite to display for the shop")]
    private SpriteRenderer shopSprite;
    
    [SerializeField]
    [Tooltip("Optional particle effect when player is nearby")]
    private ParticleSystem interactionEffect;
    
    [SerializeField]
    [Tooltip("Glow effect when player is in range")]
    private SpriteRenderer glowSprite;
    
    [Title("Debug Settings")]
    [SerializeField]
    [Tooltip("Enable debug logging for this shop trigger")]
    private bool enableDebugLogging = true;
    
    // State tracking
    private bool playerInRange = false;
    private bool shopOpen = false;
    private Transform playerTransform;
    private SpatialCollider spatialCollider;
    
    // UI references (cached)
    private static Canvas uiCanvas;
    private static Transform interactionPromptParent;
    
    // Cache references for performance (no GetComponent)
    private BaseShop[] shopComponents;
    
    #region Unity Lifecycle
    
    private void Awake()
    {
        // Cache spatial collider reference
        spatialCollider = GetComponent<SpatialCollider>();
        if (spatialCollider == null)
        {
            Debug.LogError($"[ShopTrigger] {name} requires a SpatialCollider component!");
            enabled = false;
            return;
        }
        
        // Configure spatial collider for shop interaction
        spatialCollider.SetLayer(CollisionLayers.Shop);
        spatialCollider.SetTrigger(true);
        spatialCollider.radius = interactionRange;
        
        // Cache shop components (no GetComponent during runtime)
        shopComponents = GetComponents<BaseShop>();
        if (shopComponents.Length == 0 && shop == null)
        {
            Debug.LogError($"[ShopTrigger] {name} has no BaseShop component assigned!");
            enabled = false;
            return;
        }
        
        // Use assigned shop or first found shop component
        if (shop == null && shopComponents.Length > 0)
        {
            shop = shopComponents[0];
        }
        
        // Initialize visual state
        SetGlowActive(false);
        SetInteractionEffectActive(false);
    }
    
    private void Start()
    {
        // Find player transform (cache for performance)
        if (playerTransform == null)
        {
            var player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
            }
            else if (enableDebugLogging)
            {
                Debug.LogWarning("[ShopTrigger] Player not found! Make sure player has 'Player' tag.");
            }
        }
        
        // Cache UI references
        if (uiCanvas == null)
        {
            uiCanvas = FindFirstObjectByType<Canvas>();
        }
    }
    
    private void Update()
    {
        // Handle input only when player is in range
        if (playerInRange && !shopOpen)
        {
            if (Input.GetKeyDown(interactionKey) || Input.GetKeyDown(alternativeKey))
            {
                OpenShop();
            }
        }
        
        // Close shop if player moved too far away while shop is open
        if (shopOpen && playerTransform != null)
        {
            float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);
            if (distanceToPlayer > interactionRange * 1.5f) // 50% margin
            {
                CloseShop();
            }
        }
    }
    
    #endregion
    
    #region Spatial Collision Handling
    
    public void HandleCollisionEnter(CollisionInfo collision)
    {
        // Not used - shop uses triggers only
    }
    
    public void HandleCollisionStay(CollisionInfo collision)
    {
        // Not used - shop uses triggers only
    }
    
    public void HandleCollisionExit(CollisionInfo collision)
    {
        // Not used - shop uses triggers only
    }
    
    public void HandleTriggerEnter(CollisionInfo collision)
    {
        if (collision.Other?.Layer == CollisionLayers.Player)
        {
            OnPlayerEnterRange();
        }
    }
    
    public void HandleTriggerStay(CollisionInfo collision)
    {
        // Keep player in range (could be used for continuous effects)
    }
    
    public void HandleTriggerExit(CollisionInfo collision)
    {
        if (collision.Other?.Layer == CollisionLayers.Player)
        {
            OnPlayerExitRange();
        }
    }
    
    #endregion
    
    #region Shop Interaction
    
    private void OnPlayerEnterRange()
    {
        playerInRange = true;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[ShopTrigger] Player entered range of {shop?.GetShopTitle() ?? "Unknown Shop"}");
        }
        
        // Visual feedback
        SetGlowActive(true);
        SetInteractionEffectActive(true);
        
        // Show interaction prompt
        ShowInteractionPrompt(true);
    }
    
    private void OnPlayerExitRange()
    {
        playerInRange = false;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[ShopTrigger] Player left range of {shop?.GetShopTitle() ?? "Unknown Shop"}");
        }
        
        // Visual feedback
        SetGlowActive(false);
        SetInteractionEffectActive(false);
        
        // Hide interaction prompt
        ShowInteractionPrompt(false);
        
        // Close shop if it was open
        if (shopOpen)
        {
            CloseShop();
        }
    }
    
    private void OpenShop()
    {
        if (shop == null)
        {
            Debug.LogError("[ShopTrigger] Cannot open shop - no BaseShop assigned!");
            return;
        }
        
        shopOpen = true;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[ShopTrigger] Opening {shop.GetShopTitle()}");
        }
        
        // Hide interaction prompt while shop is open
        ShowInteractionPrompt(false);
        
        // Open the shop
        shop.OpenShop();
        
        // Optional: Register with ShopManager for centralized management
        if (ShopManager.Instance != null)
        {
            // Note: We don't use ShopManager.OpenShop<T>() here because we have a direct reference
            // Just ensure no other shops are open
            ShopManager.Instance.CloseCurrentShop();
        }
    }
    
    private void CloseShop()
    {
        if (shop == null || !shopOpen) return;
        
        shopOpen = false;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[ShopTrigger] Closing {shop.GetShopTitle()}");
        }
        
        // Close the shop
        shop.CloseShop();
        
        // Show interaction prompt again if player still in range
        if (playerInRange)
        {
            ShowInteractionPrompt(true);
        }
    }
    
    #endregion
    
    #region Visual Effects
    
    private void SetGlowActive(bool active)
    {
        if (glowSprite != null)
        {
            glowSprite.gameObject.SetActive(active);
        }
    }
    
    private void SetInteractionEffectActive(bool active)
    {
        if (interactionEffect != null)
        {
            if (active && !interactionEffect.isPlaying)
            {
                interactionEffect.Play();
            }
            else if (!active && interactionEffect.isPlaying)
            {
                interactionEffect.Stop();
            }
        }
    }
    
    private void ShowInteractionPrompt(bool show)
    {
        if (!show) return; // For now, just log - UI integration can be added later
        
        string prompt = string.Format(interactionPrompt, 
            $"{interactionKey}/{alternativeKey}", 
            shop?.GetShopTitle() ?? "Shop");
            
        // TODO: Integrate with existing UI system for interaction prompts
        // For now, just log for debugging
        if (enableDebugLogging && show)
        {
            Debug.Log($"[ShopTrigger] {prompt}");
        }
    }
    
    #endregion
    
    #region ISpawnable Implementation
    
    public void OnSpawn()
    {
        // Reset state when spawned from pool
        playerInRange = false;
        shopOpen = false;
        
        // Ensure spatial collider is properly configured
        if (spatialCollider != null)
        {
            spatialCollider.SetLayer(CollisionLayers.Shop);
            spatialCollider.SetTrigger(true);
            spatialCollider.radius = interactionRange;
        }
        
        // Reset visual state
        SetGlowActive(false);
        SetInteractionEffectActive(false);
        
        // Initialize shop inventory when spawned (only once per spawn)
        if (shop != null)
        {
            // Force shop initialization but prevent repeated inventory generation
            shop.RefreshShopDisplay();
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[ShopTrigger] Spawned from pool: {shop?.GetShopTitle() ?? "Unknown Shop"}");
        }
    }
    
    public void OnDespawn()
    {
        // Clean up state when returning to pool
        if (shopOpen)
        {
            CloseShop();
        }
        
        playerInRange = false;
        
        // Stop any effects
        SetGlowActive(false);
        SetInteractionEffectActive(false);
        
        if (enableDebugLogging)
        {
            Debug.Log($"[ShopTrigger] Returned to pool: {shop?.GetShopTitle() ?? "Unknown Shop"}");
        }
    }
    
    #endregion
    
    #region Public Methods
    
    /// <summary>
    /// Set the shop for this trigger (used by spawning system)
    /// </summary>
    public void SetShop(BaseShop newShop)
    {
        shop = newShop;
        
        if (shop != null && spatialCollider != null)
        {
            // Update spatial collider reference in shop UI
            if (shop.shopUI != null)
            {
                shop.shopUI.SetOwnerShop(shop);
            }
        }
    }
    
    /// <summary>
    /// Get the current shop
    /// </summary>
    public BaseShop GetShop()
    {
        return shop;
    }
    
    /// <summary>
    /// Force close the shop (called by external systems)
    /// </summary>
    public void ForceCloseShop()
    {
        if (shopOpen)
        {
            CloseShop();
        }
    }
    
    /// <summary>
    /// Check if player is currently in interaction range
    /// </summary>
    public bool IsPlayerInRange()
    {
        return playerInRange;
    }
    
    /// <summary>
    /// Check if shop is currently open
    /// </summary>
    public bool IsShopOpen()
    {
        return shopOpen;
    }
    
    #endregion
    
    #region Debug
    
    [Title("Debug Tools")]
    [Button("Test Open Shop")]
    [PropertySpace(5)]
    private void DebugOpenShop()
    {
        if (Application.isPlaying)
        {
            OpenShop();
        }
        else
        {
            Debug.LogWarning("[ShopTrigger] Can only test shop opening in play mode");
        }
    }
    
    [Button("Test Close Shop")]
    private void DebugCloseShop()
    {
        if (Application.isPlaying)
        {
            CloseShop();
        }
        else
        {
            Debug.LogWarning("[ShopTrigger] Can only test shop closing in play mode");
        }
    }
    
    private void OnDrawGizmosSelected()
    {
        // Draw interaction range
        Gizmos.color = playerInRange ? Color.green : Color.yellow;
        Gizmos.DrawWireSphere(transform.position, interactionRange);
        
        // Draw shop connection
        if (shop != null && shop.gameObject != gameObject)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(transform.position, shop.transform.position);
        }
    }
    
    #endregion
}
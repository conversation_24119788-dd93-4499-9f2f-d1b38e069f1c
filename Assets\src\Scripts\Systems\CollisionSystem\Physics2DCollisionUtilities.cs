using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Zero-GC Physics2D collision detection utilities for high-performance collision queries.
/// Provides pre-allocated arrays and NonAlloc methods to avoid garbage collection.
/// </summary>
public static class Physics2DCollisionUtilities
{
    // Pre-allocated arrays for zero-GC collision detection
    private static readonly Collider2D[] _overlapResults = new Collider2D[64];
    private static readonly RaycastHit2D[] _castResults = new RaycastHit2D[64];
    private static readonly HashSet<int> _detectedObjectIds = new HashSet<int>();
    
    // ContactFilter2D cache for different layer combinations
    private static readonly Dictionary<int, ContactFilter2D> _contactFilterCache = new Dictionary<int, ContactFilter2D>();
    
    /// <summary>
    /// Perform zero-GC circle overlap detection
    /// </summary>
    public static int OverlapCircle(Vector2 position, float radius, LayerMask layerMask, 
        out Collider2D[] results, HitProcessingMode processingMode = HitProcessingMode.AllHits, 
        int maxHits = 10)
    {
        // Get or create ContactFilter2D
        ContactFilter2D contactFilter = GetContactFilter(layerMask);
        
        // Perform overlap query
        int hitCount = Physics2D.OverlapCircle(position, radius, contactFilter, _overlapResults);
        
        // Process results based on mode
        int processCount = GetProcessCount(hitCount, processingMode, maxHits);
        results = new Collider2D[processCount];
        
        for (int i = 0; i < processCount; i++)
        {
            results[i] = _overlapResults[i];
        }
        
        return processCount;
    }
    
    /// <summary>
    /// Perform zero-GC circle cast detection
    /// </summary>
    public static int CircleCast(Vector2 origin, float radius, Vector2 direction, float distance,
        LayerMask layerMask, out RaycastHit2D[] results, HitProcessingMode processingMode = HitProcessingMode.AllHits,
        int maxHits = 10)
    {
        // Get or create ContactFilter2D
        ContactFilter2D contactFilter = GetContactFilter(layerMask);
        
        // Perform circle cast
        int hitCount = Physics2D.CircleCast(origin, radius, direction, contactFilter, _castResults, distance);
        
        // Process results based on mode
        int processCount = GetProcessCount(hitCount, processingMode, maxHits);
        results = new RaycastHit2D[processCount];
        
        for (int i = 0; i < processCount; i++)
        {
            results[i] = _castResults[i];
        }
        
        return processCount;
    }
    
    /// <summary>
    /// Perform zero-GC box overlap detection
    /// </summary>
    public static int OverlapBox(Vector2 position, Vector2 size, float angle, LayerMask layerMask,
        out Collider2D[] results, HitProcessingMode processingMode = HitProcessingMode.AllHits,
        int maxHits = 10)
    {
        // Get or create ContactFilter2D
        ContactFilter2D contactFilter = GetContactFilter(layerMask);
        
        // Perform overlap query
        int hitCount = Physics2D.OverlapBox(position, size, angle, contactFilter, _overlapResults);
        
        // Process results based on mode
        int processCount = GetProcessCount(hitCount, processingMode, maxHits);
        results = new Collider2D[processCount];
        
        for (int i = 0; i < processCount; i++)
        {
            results[i] = _overlapResults[i];
        }
        
        return processCount;
    }
    
    /// <summary>
    /// Perform zero-GC box cast detection
    /// </summary>
    public static int BoxCast(Vector2 origin, Vector2 size, float angle, Vector2 direction, float distance,
        LayerMask layerMask, out RaycastHit2D[] results, HitProcessingMode processingMode = HitProcessingMode.AllHits,
        int maxHits = 10)
    {
        // Get or create ContactFilter2D
        ContactFilter2D contactFilter = GetContactFilter(layerMask);
        
        // Perform box cast
        int hitCount = Physics2D.BoxCast(origin, size, angle, direction, contactFilter, _castResults, distance);
        
        // Process results based on mode
        int processCount = GetProcessCount(hitCount, processingMode, maxHits);
        results = new RaycastHit2D[processCount];
        
        for (int i = 0; i < processCount; i++)
        {
            results[i] = _castResults[i];
        }
        
        return processCount;
    }
    
    /// <summary>
    /// Check if two SpatialColliders would collide using Physics2D
    /// </summary>
    public static bool CheckCollision(SpatialCollider colliderA, SpatialCollider colliderB, 
        Physics2DLayerMapping layerMapping)
    {
        if (colliderA == null || colliderB == null || layerMapping == null) return false;
        
        // Check if layers can collide
        if (!CollisionLayerMatrix.CanLayersCollide(colliderA.Layer, colliderB.Layer))
            return false;
        
        // Get Unity layer for colliderB
        int targetLayer = layerMapping.GetUnityLayer(colliderB.Layer);
        if (targetLayer == -1) return false;
        
        LayerMask targetMask = 1 << targetLayer;
        
        // Perform collision check based on colliderA's shape
        Vector2 positionA = colliderA.transform.position + (Vector3)colliderA.Offset;
        Vector2 positionB = colliderB.transform.position + (Vector3)colliderB.Offset;
        
        switch (colliderA.Shape)
        {
            case CollisionShape.Circle:
                return CheckCircleCollision(positionA, colliderA.Radius, colliderB, positionB);
                
            case CollisionShape.Box:
                return CheckBoxCollision(positionA, colliderA.Size, 0f, colliderB, positionB);
        }
        
        return false;
    }
    
    /// <summary>
    /// Check circle collision against another collider
    /// </summary>
    private static bool CheckCircleCollision(Vector2 circlePosition, float radius, 
        SpatialCollider targetCollider, Vector2 targetPosition)
    {
        switch (targetCollider.Shape)
        {
            case CollisionShape.Circle:
                float distance = Vector2.Distance(circlePosition, targetPosition);
                return distance <= (radius + targetCollider.Radius);
                
            case CollisionShape.Box:
                // Use Physics2D for box vs circle collision
                Collider2D[] results;
                return OverlapCircle(circlePosition, radius, 1 << targetCollider.gameObject.layer, 
                    out results, HitProcessingMode.FirstHitOnly) > 0;
        }
        
        return false;
    }
    
    /// <summary>
    /// Check box collision against another collider
    /// </summary>
    private static bool CheckBoxCollision(Vector2 boxPosition, Vector2 boxSize, float angle,
        SpatialCollider targetCollider, Vector2 targetPosition)
    {
        // Use Physics2D for all box collision checks
        Collider2D[] results;
        return OverlapBox(boxPosition, boxSize, angle, 1 << targetCollider.gameObject.layer,
            out results, HitProcessingMode.FirstHitOnly) > 0;
    }
    
    /// <summary>
    /// Get or create cached ContactFilter2D for the specified layer mask
    /// </summary>
    private static ContactFilter2D GetContactFilter(LayerMask layerMask)
    {
        int maskValue = layerMask.value;
        
        if (!_contactFilterCache.TryGetValue(maskValue, out ContactFilter2D filter))
        {
            filter = new ContactFilter2D
            {
                useTriggers = true,
                useLayerMask = true,
                layerMask = layerMask
            };
            _contactFilterCache[maskValue] = filter;
        }
        
        return filter;
    }
    
    /// <summary>
    /// Calculate how many hits to process based on processing mode
    /// </summary>
    private static int GetProcessCount(int hitCount, HitProcessingMode processingMode, int maxHits)
    {
        switch (processingMode)
        {
            case HitProcessingMode.FirstHitOnly:
                return hitCount > 0 ? 1 : 0;
                
            case HitProcessingMode.AllHits:
                return hitCount;
                
            case HitProcessingMode.MaxHits:
                return Mathf.Min(hitCount, maxHits);
                
            default:
                return hitCount;
        }
    }
    
    /// <summary>
    /// Clear internal caches (call when changing scenes or major state changes)
    /// </summary>
    public static void ClearCaches()
    {
        _contactFilterCache.Clear();
        _detectedObjectIds.Clear();
        
        // Clear result arrays
        System.Array.Clear(_overlapResults, 0, _overlapResults.Length);
        System.Array.Clear(_castResults, 0, _castResults.Length);
    }
    
    /// <summary>
    /// Get statistics about cache usage for debugging
    /// </summary>
    public static CollisionUtilityStats GetStats()
    {
        return new CollisionUtilityStats
        {
            ContactFilterCacheSize = _contactFilterCache.Count,
            OverlapResultsArraySize = _overlapResults.Length,
            CastResultsArraySize = _castResults.Length
        };
    }
    
    public struct CollisionUtilityStats
    {
        public int ContactFilterCacheSize;
        public int OverlapResultsArraySize;
        public int CastResultsArraySize;
    }
}

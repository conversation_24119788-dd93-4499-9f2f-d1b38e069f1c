using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Pool;

public class SpatialHashGrid
{
    private readonly float _cellSize;
    private readonly Dictionary<int, List<ICollidable>> _grid;
    private readonly Dictionary<ICollidable, List<int>> _objectCells;
    private readonly List<ICollidable> _queryBuffer;
    
    // GC-friendly reusable collections for queries
    private readonly List<(int x, int y)> _reusableCellsList;
    private readonly List<ICollidable> _reusableRemovalList;
    
    public SpatialHashGrid(float cellSize)
    {
        _cellSize = cellSize;
        _grid = new Dictionary<int, List<ICollidable>>();
        _objectCells = new Dictionary<ICollidable, List<int>>();
        _queryBuffer = new List<ICollidable>(32);
        
        // Initialize GC-friendly reusable collections
        _reusableCellsList = new List<(int x, int y)>(64);
        _reusableRemovalList = new List<ICollidable>(32);
    }
    
    public void Add(ICollidable collidable)
    {
        if (!collidable.IsActive) return;
        
        var cellIds = GetCellIds(collidable);
        _objectCells[collidable] = cellIds;
        
        foreach (var cellId in cellIds)
        {
            if (!_grid.TryGetValue(cellId, out var list))
            {
                list = ListPool<ICollidable>.Get();
                _grid[cellId] = list;
            }
            list.Add(collidable);
        }
    }
    
    public void Remove(ICollidable collidable)
    {
        if (!_objectCells.TryGetValue(collidable, out var cellIds))
            return;
            
        foreach (var cellId in cellIds)
        {
            if (_grid.TryGetValue(cellId, out var list))
            {
                list.Remove(collidable);
                if (list.Count == 0)
                {
                    ListPool<ICollidable>.Release(list);
                    _grid.Remove(cellId);
                }
            }
        }
        
        ListPool<int>.Release(cellIds);
        _objectCells.Remove(collidable);
    }
    
    public void Update(ICollidable collidable)
    {
        Remove(collidable);
        Add(collidable);
    }
    
    public List<ICollidable> GetNearby(ICollidable collidable)
    {
        _queryBuffer.Clear();
        
        if (!_objectCells.TryGetValue(collidable, out var cellIds))
            return _queryBuffer;
        
        var uniqueObjects = CollectionPool<HashSet<ICollidable>, ICollidable>.Get();
        
        foreach (var cellId in cellIds)
        {
            if (_grid.TryGetValue(cellId, out var list))
            {
                foreach (var other in list)
                {
                    if (other != collidable && other.IsActive)
                        uniqueObjects.Add(other);
                }
            }
        }
        
        // Ensure capacity before manual copy to avoid reallocations
        if (_queryBuffer.Capacity < uniqueObjects.Count)
        {
            _queryBuffer.Capacity = uniqueObjects.Count * 2;
        }
        
        // Manual copy to avoid AddRange GC allocation
        var enumerator = uniqueObjects.GetEnumerator();
        while (enumerator.MoveNext())
        {
            _queryBuffer.Add(enumerator.Current);
        }
        enumerator.Dispose();
        
        CollectionPool<HashSet<ICollidable>, ICollidable>.Release(uniqueObjects);
        return _queryBuffer;
    }
    
    /// <summary>
    /// Zero-GC version of GetNearby that fills a provided list instead of returning a new one
    /// </summary>
    public void GetNearbyNonAlloc(ICollidable collidable, List<ICollidable> results)
    {
        results.Clear();
        
        if (!_objectCells.TryGetValue(collidable, out var cellIds))
            return;
        
        var uniqueObjects = CollectionPool<HashSet<ICollidable>, ICollidable>.Get();
        
        // Use for loop to avoid IEnumerator boxing
        for (int i = 0; i < cellIds.Count; i++)
        {
            var cellId = cellIds[i];
            if (_grid.TryGetValue(cellId, out var list))
            {
                // Use for loop to avoid IEnumerator boxing
                for (int j = 0; j < list.Count; j++)
                {
                    var other = list[j];
                    if (other != collidable && other.IsActive)
                        uniqueObjects.Add(other);
                }
            }
        }
        
        // Ensure capacity before manual copy to avoid reallocations
        if (results.Capacity < uniqueObjects.Count)
        {
            results.Capacity = uniqueObjects.Count * 2;
        }
        
        // Manual copy to avoid AddRange GC allocation
        var enumerator = uniqueObjects.GetEnumerator();
        while (enumerator.MoveNext())
        {
            results.Add(enumerator.Current);
        }
        enumerator.Dispose();
        
        CollectionPool<HashSet<ICollidable>, ICollidable>.Release(uniqueObjects);
    }
    
    public void Clear()
    {
        foreach (var list in _grid.Values)
        {
            ListPool<ICollidable>.Release(list);
        }
        _grid.Clear();

        foreach (var cellIds in _objectCells.Values)
        {
            ListPool<int>.Release(cellIds);
        }
        _objectCells.Clear();
        _queryBuffer.Clear();
    }
    
    public void ClearOutsideBounds(Vector2 min, Vector2 max)
    {
        // Use reusable list to avoid GC allocation
        _reusableRemovalList.Clear();
        
        foreach (var kvp in _objectCells)
        {
            var pos = kvp.Key.Position;
            if (pos.x < min.x || pos.x > max.x || pos.y < min.y || pos.y > max.y)
            {
                _reusableRemovalList.Add(kvp.Key);
            }
        }
        
        foreach (var collidable in _reusableRemovalList)
        {
            Remove(collidable);
        }
    }
    
    private List<int> GetCellIds(ICollidable collidable)
    {
        var cellIds = ListPool<int>.Get();
        var pos = collidable.Position;
        
        float extent = 0f;
        if (collidable.Shape == ColliderShape.Circle)
            extent = collidable.Radius;
        else
            extent = Mathf.Max(collidable.Size.x, collidable.Size.y) * 0.5f;
        
        int minX = Mathf.FloorToInt((pos.x - extent) / _cellSize);
        int maxX = Mathf.FloorToInt((pos.x + extent) / _cellSize);
        int minY = Mathf.FloorToInt((pos.y - extent) / _cellSize);
        int maxY = Mathf.FloorToInt((pos.y + extent) / _cellSize);
        
        for (int x = minX; x <= maxX; x++)
        {
            for (int y = minY; y <= maxY; y++)
            {
                cellIds.Add(GetCellHash(x, y));
            }
        }
        
        return cellIds;
    }
    
    private int GetCellHash(int x, int y)
    {
        return x * 73856093 ^ y * 19349663;
    }
    
    /// <summary>
    /// Gets all cells that could contain objects within the specified radius from a position.
    /// Uses reusable collection to avoid GC allocations.
    /// </summary>
    public List<(int x, int y)> GetCellsInRadius(Vector2 position, float radius)
    {
        _reusableCellsList.Clear();
        
        int minX = Mathf.FloorToInt((position.x - radius) / _cellSize);
        int maxX = Mathf.FloorToInt((position.x + radius) / _cellSize);
        int minY = Mathf.FloorToInt((position.y - radius) / _cellSize);
        int maxY = Mathf.FloorToInt((position.y + radius) / _cellSize);
        
        for (int x = minX; x <= maxX; x++)
        {
            for (int y = minY; y <= maxY; y++)
            {
                _reusableCellsList.Add((x, y));
            }
        }
        
        return _reusableCellsList;
    }
    
    /// <summary>
    /// Gets all collidables in a specific cell.
    /// </summary>
    public List<ICollidable> GetCollidablesInCell(int x, int y)
    {
        int cellHash = GetCellHash(x, y);
        return _grid.TryGetValue(cellHash, out var list) ? list : null;
    }
    
    /// <summary>
    /// Get all collidables within a radius from a specific position.
    /// Zero-GC method that fills the provided results list.
    /// </summary>
    /// <param name="position">Center position for radius query</param>
    /// <param name="radius">Search radius</param>
    /// <param name="results">List to fill with results (will be cleared first)</param>
    public void GetObjectsInRadius(Vector2 position, float radius, List<ICollidable> results)
    {
        results.Clear();
        
        // Get cells that could contain objects within radius
        var cellsInRadius = GetCellsInRadius(position, radius);
        var uniqueObjects = CollectionPool<HashSet<ICollidable>, ICollidable>.Get();
        
        // Collect all objects from relevant cells
        for (int i = 0; i < cellsInRadius.Count; i++)
        {
            var cellCoord = cellsInRadius[i];
            int cellHash = GetCellHash(cellCoord.x, cellCoord.y);
            
            if (_grid.TryGetValue(cellHash, out var list))
            {
                for (int j = 0; j < list.Count; j++)
                {
                    var collidable = list[j];
                    if (collidable.IsActive)
                    {
                        uniqueObjects.Add(collidable);
                    }
                }
            }
        }
        
        // Ensure capacity before manual copy to avoid reallocations
        if (results.Capacity < uniqueObjects.Count)
        {
            results.Capacity = uniqueObjects.Count * 2;
        }
        
        // Manual copy to avoid AddRange GC allocation
        var enumerator = uniqueObjects.GetEnumerator();
        while (enumerator.MoveNext())
        {
            results.Add(enumerator.Current);
        }
        enumerator.Dispose();
        
        CollectionPool<HashSet<ICollidable>, ICollidable>.Release(uniqueObjects);
    }
}
using UnityEngine;
using System.Collections.Generic;
using Sirenix.OdinInspector;

/// <summary>
/// Maps CollisionLayers enum to Unity Physics2D layers for seamless integration
/// between custom collision logic and Unity's Physics2D system.
/// Provides bidirectional conversion and layer mask generation.
/// </summary>
[CreateAssetMenu(fileName = "Physics2DLayerMapping", menuName = "2D Rogue/Collision/Physics2D Layer Mapping")]
public class Physics2DLayerMapping : ScriptableObject
{
    [Title("Layer Mapping Configuration")]
    [InfoBox("Maps CollisionLayers enum values to Unity Physics2D layers. " +
             "Ensure Unity layers are properly configured in Project Settings > Tags and Layers.")]
    
    [SerializeField, TableList(ShowIndexLabels = true)]
    private LayerMappingEntry[] layerMappings = new LayerMappingEntry[]
    {
        new LayerMappingEntry(CollisionLayers.Player, "Player"),
        new LayerMappingEntry(CollisionLayers.Enemy, "Enemy"),
        new LayerMappingEntry(CollisionLayers.PlayerProjectile, "PlayerProjectile"),
        new LayerMappingEntry(CollisionLayers.EnemyProjectile, "EnemyProjectile"),
        new LayerMappingEntry(CollisionLayers.Wall, "Wall"),
        new LayerMappingEntry(CollisionLayers.Trigger, "Trigger"),
        new LayerMappingEntry(CollisionLayers.Pickup, "Pickup"),
        new LayerMappingEntry(CollisionLayers.Environment, "Environment"),
        new LayerMappingEntry(CollisionLayers.Interactable, "Interactable"),
        new LayerMappingEntry(CollisionLayers.Vegetation, "Vegetation"),
        new LayerMappingEntry(CollisionLayers.Breach, "Breach"),
        new LayerMappingEntry(CollisionLayers.Shop, "Shop")
    };
    
    // Cached mappings for performance
    private Dictionary<CollisionLayers, int> _collisionToUnityLayer;
    private Dictionary<int, CollisionLayers> _unityToCollisionLayer;
    private bool _cachesInitialized = false;
    
    [System.Serializable]
    public class LayerMappingEntry
    {
        [HorizontalGroup("Mapping")]
        [LabelText("Collision Layer"), LabelWidth(100)]
        public CollisionLayers collisionLayer;
        
        [HorizontalGroup("Mapping")]
        [LabelText("Unity Layer"), LabelWidth(80)]
        public string unityLayerName;
        
        [HorizontalGroup("Mapping")]
        [LabelText("Layer ID"), LabelWidth(60), ReadOnly]
        public int unityLayerId => LayerMask.NameToLayer(unityLayerName);
        
        public LayerMappingEntry(CollisionLayers collision, string unity)
        {
            collisionLayer = collision;
            unityLayerName = unity;
        }
    }
    
    /// <summary>
    /// Initialize cached mappings for fast lookup
    /// </summary>
    private void InitializeCaches()
    {
        if (_cachesInitialized) return;
        
        _collisionToUnityLayer = new Dictionary<CollisionLayers, int>();
        _unityToCollisionLayer = new Dictionary<int, CollisionLayers>();
        
        foreach (var mapping in layerMappings)
        {
            int unityLayer = LayerMask.NameToLayer(mapping.unityLayerName);
            if (unityLayer != -1)
            {
                _collisionToUnityLayer[mapping.collisionLayer] = unityLayer;
                _unityToCollisionLayer[unityLayer] = mapping.collisionLayer;
            }
            else
            {
                Debug.LogWarning($"Unity layer '{mapping.unityLayerName}' not found for CollisionLayer '{mapping.collisionLayer}'");
            }
        }
        
        _cachesInitialized = true;
    }
    
    /// <summary>
    /// Convert CollisionLayers to Unity layer ID
    /// </summary>
    public int GetUnityLayer(CollisionLayers collisionLayer)
    {
        InitializeCaches();
        return _collisionToUnityLayer.TryGetValue(collisionLayer, out int layer) ? layer : -1;
    }
    
    /// <summary>
    /// Convert Unity layer ID to CollisionLayers
    /// </summary>
    public CollisionLayers GetCollisionLayer(int unityLayer)
    {
        InitializeCaches();
        return _unityToCollisionLayer.TryGetValue(unityLayer, out CollisionLayers layer) ? layer : CollisionLayers.None;
    }
    
    /// <summary>
    /// Create LayerMask for Physics2D queries from CollisionLayers
    /// </summary>
    public LayerMask CreateLayerMask(CollisionLayers collisionLayers)
    {
        InitializeCaches();
        LayerMask mask = 0;
        
        // Check each individual layer flag
        foreach (CollisionLayers layer in System.Enum.GetValues(typeof(CollisionLayers)))
        {
            if (layer != CollisionLayers.None && layer != CollisionLayers.All && 
                (collisionLayers & layer) == layer)
            {
                int unityLayer = GetUnityLayer(layer);
                if (unityLayer != -1)
                {
                    mask |= (1 << unityLayer);
                }
            }
        }
        
        return mask;
    }
    
    /// <summary>
    /// Create LayerMask for layers that can collide with the specified layer
    /// </summary>
    public LayerMask CreateCollisionMask(CollisionLayers sourceLayer)
    {
        LayerMask mask = 0;
        
        // Check against all layers using CollisionLayerMatrix
        foreach (CollisionLayers targetLayer in System.Enum.GetValues(typeof(CollisionLayers)))
        {
            if (targetLayer != CollisionLayers.None && targetLayer != CollisionLayers.All &&
                CollisionLayerMatrix.CanLayersCollide(sourceLayer, targetLayer))
            {
                int unityLayer = GetUnityLayer(targetLayer);
                if (unityLayer != -1)
                {
                    mask |= (1 << unityLayer);
                }
            }
        }
        
        return mask;
    }
    
    [Title("Validation")]
    [Button("Validate Layer Mappings")]
    private void ValidateLayerMappings()
    {
        bool allValid = true;
        
        foreach (var mapping in layerMappings)
        {
            int layerId = LayerMask.NameToLayer(mapping.unityLayerName);
            if (layerId == -1)
            {
                Debug.LogError($"Unity layer '{mapping.unityLayerName}' not found! Please create it in Project Settings > Tags and Layers.");
                allValid = false;
            }
        }
        
        if (allValid)
        {
            Debug.Log("All layer mappings are valid!");
        }
        
        // Clear caches to force re-initialization
        _cachesInitialized = false;
    }
    
    [Button("Log Layer Mapping Info")]
    private void LogLayerMappingInfo()
    {
        InitializeCaches();
        
        Debug.Log("=== Physics2D Layer Mapping ===");
        foreach (var mapping in layerMappings)
        {
            int layerId = LayerMask.NameToLayer(mapping.unityLayerName);
            Debug.Log($"{mapping.collisionLayer} -> '{mapping.unityLayerName}' (ID: {layerId})");
        }
    }
}

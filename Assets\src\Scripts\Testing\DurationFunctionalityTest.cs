using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Test script to verify that the duration functionality works correctly with support gems.
/// Tests the new skillDurationMultiplier property and tooltip integration.
/// </summary>
public class DurationFunctionalityTest : MonoBehaviour
{
    [Title("Test Configuration")]
    [InfoBox("This test verifies that duration modifiers from support gems work correctly")]
    
    [SerializeField, Required]
    [Tooltip("A skill gem with duration > 0 for testing")]
    private SkillGemData testSkillGem;
    
    [SerializeField, Required]
    [Tooltip("A support gem with skillDurationMultiplier != 1.0")]
    private SupportGemData testSupportGem;
    
    [Title("Test Results")]
    [ShowInInspector, ReadOnly]
    private float baseDuration;
    
    [ShowInInspector, ReadOnly]
    private float supportDurationMultiplier;
    
    [ShowInInspector, ReadOnly]
    private float finalDuration;
    
    [ShowInInspector, ReadOnly]
    private string tooltipText;
    
    [Title("Debug Settings")]
    [SerializeField]
    private bool enableDebugLogging = true;
    
    [But<PERSON>("Run Duration Test")]
    private void RunDurationTest()
    {
        if (testSkillGem == null)
        {
            Debug.LogError("Test skill gem is not assigned!");
            return;
        }
        
        if (testSupportGem == null)
        {
            Debug.LogError("Test support gem is not assigned!");
            return;
        }
        
        // Test 1: Basic duration values
        baseDuration = testSkillGem.duration;
        supportDurationMultiplier = testSupportGem.skillDurationMultiplier;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[DurationTest] Base skill duration: {baseDuration}s");
            Debug.Log($"[DurationTest] Support gem duration multiplier: {supportDurationMultiplier}x");
        }
        
        // Test 2: Create gem instances and test calculation
        var skillInstance = new GemInstance(testSkillGem, GemRarity.Common);
        var supportInstance = new GemInstance(testSupportGem, GemRarity.Common);
        
        // Test the GetSupportSkillDurationMultiplier method
        float calculatedMultiplier = supportInstance.GetSupportSkillDurationMultiplier();
        
        if (enableDebugLogging)
        {
            Debug.Log($"[DurationTest] Calculated support multiplier: {calculatedMultiplier}x");
        }
        
        // Test 3: Test with GemSocketController
        var controller = new GemSocketController
        {
            skillGemInstance = skillInstance,
            supportGemInstances = new System.Collections.Generic.List<GemInstance> { supportInstance }
        };
        
        finalDuration = controller.CalculateFinalDuration();
        
        if (enableDebugLogging)
        {
            Debug.Log($"[DurationTest] Final calculated duration: {finalDuration}s");
            Debug.Log($"[DurationTest] Expected duration: {baseDuration * supportDurationMultiplier}s");
        }
        
        // Test 4: Verify calculation is correct
        float expectedDuration = baseDuration * supportDurationMultiplier;
        bool calculationCorrect = Mathf.Abs(finalDuration - expectedDuration) < 0.01f;
        
        if (calculationCorrect)
        {
            Debug.Log($"<color=green>[DurationTest] ✓ Duration calculation is correct!</color>");
        }
        else
        {
            Debug.LogError($"[DurationTest] ✗ Duration calculation is incorrect! Expected: {expectedDuration}, Got: {finalDuration}");
        }
        
        // Test 5: Test tooltip generation
        TestTooltipGeneration(skillInstance, supportInstance);
    }
    
    private void TestTooltipGeneration(GemInstance skillInstance, GemInstance supportInstance)
    {
        if (enableDebugLogging)
        {
            Debug.Log("[DurationTest] Testing tooltip generation...");
        }
        
        // Test skill gem tooltip
        string skillTooltip = skillInstance.GetTooltipText();
        bool skillHasDuration = skillTooltip.Contains("Duration:");
        
        if (enableDebugLogging)
        {
            Debug.Log($"[DurationTest] Skill gem tooltip contains duration: {skillHasDuration}");
        }
        
        // Test support gem tooltip
        string supportTooltip = supportInstance.GetTooltipText();
        bool supportHasDuration = supportTooltip.Contains("Duration:");
        
        if (enableDebugLogging)
        {
            Debug.Log($"[DurationTest] Support gem tooltip contains duration: {supportHasDuration}");
            Debug.Log($"[DurationTest] Support gem tooltip:\n{supportTooltip}");
        }
        
        tooltipText = supportTooltip;
        
        // Test combined tooltip (simulating equipped gems)
        var controller = new GemSocketController
        {
            skillGemInstance = skillInstance,
            supportGemInstances = new System.Collections.Generic.List<GemInstance> { supportInstance }
        };
        
        // This would normally be done by SkillGemSlot, but we can test the calculation
        float baseDur = (skillInstance.gemDataTemplate as SkillGemData).duration;
        float finalDur = controller.CalculateFinalDuration();
        
        if (Mathf.Abs(finalDur - baseDur) > 0.01f)
        {
            float durationPercent = ((finalDur / baseDur) - 1f) * 100f;
            string combinedDurationText = $"Duration: {baseDur:F1}s → {finalDur:F1}s ({durationPercent:+0;-0}%)";
            
            if (enableDebugLogging)
            {
                Debug.Log($"[DurationTest] Combined duration display: {combinedDurationText}");
            }
        }
        
        Debug.Log("<color=green>[DurationTest] ✓ Tooltip generation test completed!</color>");
    }
    
    [Button("Test Only Tooltip Formatting")]
    private void TestTooltipFormatting()
    {
        if (testSupportGem == null)
        {
            Debug.LogError("Test support gem is not assigned!");
            return;
        }

        var supportInstance = new GemInstance(testSupportGem, GemRarity.Common);
        string tooltip = supportInstance.GetTooltipText();

        Debug.Log($"[DurationTest] Support gem tooltip:\n{tooltip}");
        tooltipText = tooltip;

        // Check if duration modifier is displayed correctly
        bool hasDurationModifier = tooltip.Contains("Duration:") && tooltip.Contains("%");

        if (hasDurationModifier)
        {
            Debug.Log("<color=green>[DurationTest] ✓ Duration modifier is displayed in tooltip!</color>");
        }
        else
        {
            Debug.LogWarning("[DurationTest] Duration modifier not found in tooltip. Check if skillDurationMultiplier != 1.0");
        }
    }

    [Button("Test GemSelection UI Tooltips")]
    private void TestGemSelectionTooltips()
    {
        if (testSkillGem == null || testSupportGem == null)
        {
            Debug.LogError("Test gems are not assigned!");
            return;
        }

        // Test skill gem selection tooltip
        var skillInstance = new GemInstance(testSkillGem, GemRarity.Common);
        var selectableSkillGem = new SelectableGem(skillInstance);
        string skillSelectionTooltip = selectableSkillGem.GetFormattedDescription();

        Debug.Log($"[DurationTest] Skill gem selection tooltip:\n{skillSelectionTooltip}");

        // Check if skill gem shows duration
        bool skillHasDuration = skillSelectionTooltip.Contains("Duration:") && skillSelectionTooltip.Contains("s");

        if (skillHasDuration)
        {
            Debug.Log("<color=green>[DurationTest] ✓ Skill gem selection tooltip shows duration!</color>");
        }
        else
        {
            Debug.LogWarning("[DurationTest] Skill gem selection tooltip missing duration. Check if skill has duration > 0");
        }

        // Test support gem selection tooltip
        var supportInstance = new GemInstance(testSupportGem, GemRarity.Common);
        var selectableSupportGem = new SelectableGem(supportInstance);
        string supportSelectionTooltip = selectableSupportGem.GetFormattedDescription();

        Debug.Log($"[DurationTest] Support gem selection tooltip:\n{supportSelectionTooltip}");

        // Check if support gem shows duration modifier
        bool supportHasDurationModifier = supportSelectionTooltip.Contains("Duration:") && supportSelectionTooltip.Contains("%");

        if (supportHasDurationModifier)
        {
            Debug.Log("<color=green>[DurationTest] ✓ Support gem selection tooltip shows duration modifier!</color>");
        }
        else
        {
            Debug.LogWarning("[DurationTest] Support gem selection tooltip missing duration modifier. Check if skillDurationMultiplier != 1.0");
        }

        Debug.Log("<color=green>[DurationTest] ✓ GemSelection UI tooltip test completed!</color>");
    }

    [Button("Test New Tag System Integration")]
    private void TestNewTagSystemIntegration()
    {
        if (testSkillGem == null || testSupportGem == null)
        {
            Debug.LogError("Test gems are not assigned!");
            return;
        }

        Debug.Log("[DurationTest] Testing new tag system integration with duration functionality...");

        // Test that gems with Duration tag work with duration support gems
        var skillInstance = new GemInstance(testSkillGem, GemRarity.Common);
        var supportInstance = new GemInstance(testSupportGem, GemRarity.Common);

        // Check if skill has Duration tag
        bool skillHasDurationTag = (testSkillGem.gemTags & GemTag.Duration) != 0;

        // Check if support gem is compatible with Duration tag
        bool supportCompatibleWithDuration = (testSupportGem.compatibleTags & GemTag.Duration) != 0;

        // Test compatibility
        bool areCompatible = (testSkillGem.gemTags & testSupportGem.compatibleTags) != GemTag.None;

        Debug.Log($"[DurationTest] Skill has Duration tag: {skillHasDurationTag}");
        Debug.Log($"[DurationTest] Support compatible with Duration: {supportCompatibleWithDuration}");
        Debug.Log($"[DurationTest] Gems are compatible: {areCompatible}");

        // Test tooltip shows new tags
        string skillTooltip = skillInstance.GetTooltipText();
        string supportTooltip = supportInstance.GetTooltipText();

        bool skillTooltipHasNewTags = skillTooltip.Contains("Duration") || skillTooltip.Contains("AoE") || skillTooltip.Contains("Ailment");
        bool supportTooltipHasNewTags = supportTooltip.Contains("Duration") || supportTooltip.Contains("AoE") || supportTooltip.Contains("Ailment");

        Debug.Log($"[DurationTest] Skill tooltip shows new tags: {skillTooltipHasNewTags}");
        Debug.Log($"[DurationTest] Support tooltip shows new tags: {supportTooltipHasNewTags}");

        if (skillTooltipHasNewTags || supportTooltipHasNewTags)
        {
            Debug.Log("<color=green>[DurationTest] ✓ New tag system is integrated with duration functionality!</color>");
        }
        else
        {
            Debug.LogWarning("[DurationTest] New tags not visible in tooltips. Check gem configurations.");
        }
    }

    [Button("Test Conditional Duration Display")]
    private void TestConditionalDurationDisplay()
    {
        Debug.Log("[DurationTest] Testing conditional duration display based on Duration tag...");

        // Find gems to test with
        var allSkillGems = Resources.FindObjectsOfTypeAll<SkillGemData>();

        foreach (var skillGem in allSkillGems)
        {
            if (skillGem.duration > 0f)
            {
                bool hasDurationTag = (skillGem.gemTags & GemTag.Duration) != 0;
                var instance = new GemInstance(skillGem, GemRarity.Common);
                string tooltip = instance.GetTooltipText();
                bool tooltipShowsDuration = tooltip.Contains("Duration:") && tooltip.Contains("s");

                Debug.Log($"[DurationTest] {skillGem.gemName}: duration={skillGem.duration:F1}s, hasDurationTag={hasDurationTag}, tooltipShowsDuration={tooltipShowsDuration}");

                // Test selection UI tooltip too
                var selectable = new SelectableGem(instance);
                string selectionTooltip = selectable.GetFormattedDescription();
                bool selectionShowsDuration = selectionTooltip.Contains("Duration:") && selectionTooltip.Contains("s");

                Debug.Log($"[DurationTest] {skillGem.gemName} selection UI: tooltipShowsDuration={selectionShowsDuration}");

                // Verify logic: should show duration only if has Duration tag
                bool shouldShowDuration = hasDurationTag;
                bool actuallyShowsDuration = tooltipShowsDuration;

                if (shouldShowDuration == actuallyShowsDuration)
                {
                    Debug.Log($"<color=green>[DurationTest] ✓ {skillGem.gemName}: Correct duration display logic!</color>");
                }
                else
                {
                    Debug.LogError($"[DurationTest] ✗ {skillGem.gemName}: Duration display logic incorrect! Should show: {shouldShowDuration}, Actually shows: {actuallyShowsDuration}");
                }
            }
        }

        Debug.Log("<color=green>[DurationTest] ✓ Conditional duration display test completed!</color>");
    }
}

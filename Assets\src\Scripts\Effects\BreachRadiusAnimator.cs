using UnityEngine;
using Sirenix.OdinInspector;
using PrimeTween;

/// <summary>
/// Handles SpatialCollider radius animation for breach effects.
/// Separated from BreachEffect to follow Single Responsibility Principle.
/// </summary>
[RequireComponent(typeof(SpatialCollider))]
public class BreachRadiusAnimator : MonoBehaviour
{
    [Title("Radius Animation")]
    [InfoBox("Animates SpatialCollider radius over time using settings from BreachEffectSettings. Enable 'Use Custom Values' to override specific parameters.", InfoMessageType.Info)]
    
    [SerializeField]
    [Tooltip("Settings asset containing default values for all breach effects")]
    private BreachEffectSettings settings;
    
    [SerializeField]
    [Tooltip("Enable radius animation")]
    private bool enableAnimation = true;
    
    [ShowIf("enableAnimation")]
    [SerializeField]
    [Tooltip("Use custom values instead of settings defaults")]
    private bool useCustomValues = false;
    
    [Title("Custom Overrides")]
    [ShowIf("@enableAnimation && useCustomValues")]
    [SerializeField, MinValue(0.1f)]
    [Tooltip("Start radius for the collider (overrides settings)")]
    private float customStartRadius = 0.1f;
    
    [ShowIf("@enableAnimation && useCustomValues")]
    [SerializeField, MinValue(0.1f)]
    [Tooltip("End radius for the collider (overrides settings)")]
    private float customEndRadius = 5f;
    
    [ShowIf("@enableAnimation && useCustomValues")]
    [SerializeField, MinValue(0.01f)]
    [Tooltip("Duration for radius animation in seconds (overrides settings)")]
    private float customDuration = 1.5f;
    
    [ShowIf("@enableAnimation && useCustomValues")]
    [SerializeField]
    [Tooltip("Easing function for radius animation (overrides settings)")]
    private Ease customEase = Ease.OutBack;
    
    [ShowIf("@enableAnimation && useCustomValues")]
    [SerializeField]
    [Tooltip("Optional delay before starting radius animation (overrides settings)")]
    private float customDelay = 0f;
    
    [SerializeField]
    [Tooltip("Reset to start radius on disable")]
    private bool resetOnDisable = true;
    
    [SerializeField]
    [Tooltip("Enable debug logging")]
    private bool enableDebugLogging = false;
    
    // Runtime references
    private SpatialCollider spatialCollider;
    private float originalRadius;
    private Tween animationTween;
    private bool isPlaying = false;
    
    // Properties for external access
    public bool IsPlaying => isPlaying;
    public float CurrentRadius => spatialCollider != null ? spatialCollider.radius : 0f;
    public bool EnableAnimation 
    { 
        get => enableAnimation; 
        set => enableAnimation = value; 
    }
    
    // Settings-aware properties that return either custom values or settings defaults
    public float StartRadius => useCustomValues ? customStartRadius : (settings?.defaultRadiusStart ?? 0.1f);
    public float EndRadius => useCustomValues ? customEndRadius : (settings?.defaultRadiusEnd ?? 5f);
    public float Duration => useCustomValues ? customDuration : (settings?.defaultRadiusDuration ?? 1.5f);
    public Ease AnimationEase => useCustomValues ? customEase : (settings?.defaultRadiusEase ?? Ease.OutBack);
    public float Delay => useCustomValues ? customDelay : (settings?.defaultRadiusDelay ?? 0f);
    
    #region Unity Lifecycle
    void Awake()
    {
        spatialCollider = GetComponent<SpatialCollider>();
        
        // Store original radius
        if (spatialCollider != null)
        {
            originalRadius = spatialCollider.radius;
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachRadiusAnimator] {gameObject.name}: Initialized with original radius {originalRadius}");
        }
    }
    
    void OnDisable()
    {
        StopAnimation();
        
        if (resetOnDisable && enableAnimation)
        {
            ResetToStartRadius();
        }
    }
    #endregion
    
    #region Public Methods
    /// <summary>
    /// Starts the radius animation
    /// </summary>
    [Button("Play Animation", ButtonSizes.Large)]
    public void PlayAnimation()
    {
        if (!enableAnimation)
        {
            if (enableDebugLogging)
            {
                Debug.LogWarning($"[BreachRadiusAnimator] {gameObject.name}: Animation disabled");
            }
            return;
        }
        
        if (isPlaying)
        {
            if (enableDebugLogging)
            {
                Debug.LogWarning($"[BreachRadiusAnimator] {gameObject.name}: Already playing, stopping current animation");
            }
            StopAnimation();
        }
        
        if (spatialCollider == null)
        {
            if (enableDebugLogging)
            {
                Debug.LogWarning($"[BreachRadiusAnimator] {gameObject.name}: Cannot play animation - missing SpatialCollider");
            }
            return;
        }
        
        isPlaying = true;
        
        // Set start radius
        spatialCollider.radius = StartRadius;
        spatialCollider.ForceCollisionUpdate();
        
        // Create animation with optional delay and collision updates during animation
        if (Delay > 0f)
        {
            animationTween = Tween.Delay(Delay, useUnscaledTime: true)
                .OnComplete(() => {
                    animationTween = Tween.Custom(StartRadius, EndRadius, Duration,
                        value => {
                            spatialCollider.radius = value;
                            spatialCollider.ForceCollisionUpdate();
                        }, AnimationEase)
                        .OnComplete(() => isPlaying = false);
                });
        }
        else
        {
            animationTween = Tween.Custom(StartRadius, EndRadius, Duration,
                value => {
                    spatialCollider.radius = value;
                    spatialCollider.ForceCollisionUpdate();
                }, AnimationEase)
                .OnComplete(() => isPlaying = false);
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachRadiusAnimator] {gameObject.name}: Started animation - {StartRadius} → {EndRadius} over {Duration}s");
        }
    }
    
    /// <summary>
    /// Stops the radius animation
    /// </summary>
    [Button("Stop Animation", ButtonSizes.Medium)]
    public void StopAnimation()
    {
        animationTween.Stop();
        isPlaying = false;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[BreachRadiusAnimator] {gameObject.name}: Stopped animation");
        }
    }
    
    /// <summary>
    /// Resets radius to start value
    /// </summary>
    [Button("Reset To Start Radius", ButtonSizes.Medium)]
    public void ResetToStartRadius()
    {
        if (enableAnimation && spatialCollider != null)
        {
            SetRadius(StartRadius);
            
            if (enableDebugLogging)
            {
                Debug.Log($"[BreachRadiusAnimator] {gameObject.name}: Reset radius to {StartRadius}");
            }
        }
    }
    
    /// <summary>
    /// Sets radius directly with collision update
    /// </summary>
    public void SetRadius(float newRadius)
    {
        if (spatialCollider != null)
        {
            spatialCollider.radius = newRadius;
            spatialCollider.ForceCollisionUpdate();
        }
    }
    
    /// <summary>
    /// Gets the current animated radius progress (0-1)
    /// </summary>
    public float GetAnimationProgress()
    {
        if (!enableAnimation || StartRadius >= EndRadius)
            return 0f;
            
        float current = CurrentRadius;
        return Mathf.Clamp01((current - StartRadius) / (EndRadius - StartRadius));
    }
    #endregion
    
    #region Debug & Inspector
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    [ShowIf("@UnityEngine.Application.isPlaying")]
    private string runtimeStatus => $"Playing: {isPlaying} | Radius: {CurrentRadius:F2} | Progress: {GetAnimationProgress():P0} | Source: {(useCustomValues ? "Custom" : "Settings")}";
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    [ShowIf("@UnityEngine.Application.isPlaying && !useCustomValues")]
    private string settingsInfo => settings != null ? $"Using settings: {settings.name}" : "❌ Settings not assigned!";
    
    #if UNITY_EDITOR
    [Button("Test Animation", ButtonSizes.Medium)]
    private void TestAnimation()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test animation in play mode!");
            return;
        }
        
        PlayAnimation();
    }
    
    [Button("Set Start Radius", ButtonSizes.Small)]
    private void SetStartRadiusValue()
    {
        if (!Application.isPlaying) return;
        SetRadius(StartRadius);
        Debug.Log($"Set radius to {StartRadius}");
    }
    
    [Button("Set End Radius", ButtonSizes.Small)]
    private void SetEndRadiusValue()
    {
        if (!Application.isPlaying) return;
        SetRadius(EndRadius);
        Debug.Log($"Set radius to {EndRadius}");
    }
    
    [Button("Animate To Radius", ButtonSizes.Small)]
    [PropertySpace(10)]
    private void AnimateToRadius([MinValue(0.1f)] float targetRadius)
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only animate in play mode!");
            return;
        }
        
        if (spatialCollider == null) return;
        
        float currentRadius = spatialCollider.radius;
        animationTween.Stop();
        isPlaying = true;
        
        animationTween = Tween.Custom(currentRadius, targetRadius, Duration,
            value => {
                spatialCollider.radius = value;
                spatialCollider.ForceCollisionUpdate();
            }, AnimationEase)
            .OnComplete(() => isPlaying = false);
            
        Debug.Log($"Animating radius from {currentRadius:F2} to {targetRadius:F2}");
    }
    #endif
    #endregion
}
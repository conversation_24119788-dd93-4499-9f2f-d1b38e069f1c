using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Validation tool for testing collision-dependent systems with both Custom and Physics2D collision detection modes.
/// This ensures all systems work correctly after the collision system refactoring.
/// </summary>
public class CollisionSystemValidator : MonoBehaviour
{
    [Title("Validation Settings")]
    [SerializeField] private bool enableDetailedLogging = true;
    [SerializeField] private bool validateOnStart = false;
    
    [Title("Test Results")]
    [ShowInInspector, ReadOnly] private List<ValidationResult> validationResults = new List<ValidationResult>();
    
    [System.Serializable]
    public class ValidationResult
    {
        public string systemName;
        public bool customModeWorking;
        public bool physics2DModeWorking;
        public string notes;
        
        public ValidationResult(string name, bool custom, bool physics2D, string notes = "")
        {
            this.systemName = name;
            this.customModeWorking = custom;
            this.physics2DModeWorking = physics2D;
            this.notes = notes;
        }
    }
    
    private void Start()
    {
        if (validateOnStart)
        {
            ValidateAllSystems();
        }
    }
    
    [Button("Validate All Collision-Dependent Systems", ButtonSizes.Large)]
    public void ValidateAllSystems()
    {
        validationResults.Clear();
        
        if (CollisionManager.Instance == null)
        {
            Debug.LogError("[CollisionValidator] CollisionManager.Instance is null! Cannot perform validation.");
            return;
        }
        
        Debug.Log("[CollisionValidator] Starting comprehensive collision system validation...");
        
        // Test both collision modes
        ValidateCollisionMode(CollisionDetectionMode.Custom);
        ValidateCollisionMode(CollisionDetectionMode.Physics2D);
        
        // Generate summary report
        GenerateValidationReport();
    }
    
    private void ValidateCollisionMode(CollisionDetectionMode mode)
    {
        Debug.Log($"[CollisionValidator] Testing {mode} collision detection mode...");
        
        // Switch to the test mode
        var originalMode = CollisionManager.Instance.GetCollisionMode();
        SetCollisionMode(mode);
        
        // Wait a frame for mode switch to take effect
        StartCoroutine(ValidateSystemsAfterModeSwitch(mode, originalMode));
    }
    
    private System.Collections.IEnumerator ValidateSystemsAfterModeSwitch(CollisionDetectionMode testMode, CollisionDetectionMode originalMode)
    {
        yield return null; // Wait one frame
        
        // Test all collision-dependent systems
        ValidateProjectileSystems(testMode);
        ValidatePickupSystems(testMode);
        ValidateEnemySystems(testMode);
        ValidateTriggerSystems(testMode);
        ValidatePlayerSystems(testMode);
        
        // Restore original mode
        SetCollisionMode(originalMode);
    }
    
    private void SetCollisionMode(CollisionDetectionMode mode)
    {
        // Use reflection to set the collision mode since there's no public setter
        var collisionManagerType = typeof(CollisionManager);
        var collisionModeField = collisionManagerType.GetField("collisionMode", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (collisionModeField != null)
        {
            collisionModeField.SetValue(CollisionManager.Instance, mode);
            Debug.Log($"[CollisionValidator] Switched to {mode} mode");
        }
        else
        {
            Debug.LogError("[CollisionValidator] Could not access collisionMode field!");
        }
    }
    
    private void ValidateProjectileSystems(CollisionDetectionMode mode)
    {
        var projectiles = FindObjectsOfType<Projectile>();
        var serpentineProjectiles = FindObjectsOfType<SerpentineProjectile>();
        var magmaBalls = FindObjectsOfType<MagmaBall>();
        var orbitingBlades = FindObjectsOfType<OrbitingBlade>();
        
        bool allWorking = true;
        string notes = $"Found {projectiles.Length} Projectiles, {serpentineProjectiles.Length} SerpentineProjectiles, " +
                      $"{magmaBalls.Length} MagmaBalls, {orbitingBlades.Length} OrbitingBlades";
        
        // Test projectile collision detection
        foreach (var projectile in projectiles)
        {
            if (!ValidateProjectileCollision(projectile))
            {
                allWorking = false;
                break;
            }
        }
        
        AddValidationResult("Projectile Systems", mode, allWorking, notes);
    }
    
    private bool ValidateProjectileCollision(Projectile projectile)
    {
        var spatialCollider = projectile.GetComponent<SpatialCollider>();
        if (spatialCollider == null)
        {
            Debug.LogWarning($"[CollisionValidator] Projectile {projectile.name} missing SpatialCollider!");
            return false;
        }
        
        // Check if collision handlers are properly set up
        var handlers = projectile.GetComponents<ISpatialCollisionHandler>();
        if (handlers == null || handlers.Length == 0)
        {
            Debug.LogWarning($"[CollisionValidator] Projectile {projectile.name} missing ISpatialCollisionHandler!");
            return false;
        }
        
        return true;
    }
    
    private void ValidatePickupSystems(CollisionDetectionMode mode)
    {
        var splinterPickups = FindObjectsOfType<SplinterPickup>();
        var currencyPickups = FindObjectsOfType<CurrencyPickup>();
        
        bool allWorking = true;
        string notes = $"Found {splinterPickups.Length} SplinterPickups, {currencyPickups.Length} CurrencyPickups";
        
        // Test pickup trigger detection
        foreach (var pickup in splinterPickups)
        {
            if (!ValidatePickupTrigger(pickup.GetComponent<SpatialCollider>()))
            {
                allWorking = false;
                break;
            }
        }
        
        foreach (var pickup in currencyPickups)
        {
            if (!ValidatePickupTrigger(pickup.GetComponent<SpatialCollider>()))
            {
                allWorking = false;
                break;
            }
        }
        
        AddValidationResult("Pickup Systems", mode, allWorking, notes);
    }
    
    private bool ValidatePickupTrigger(SpatialCollider spatialCollider)
    {
        if (spatialCollider == null) return false;
        
        // Pickups should be triggers
        if (!spatialCollider.IsTrigger)
        {
            Debug.LogWarning($"[CollisionValidator] Pickup {spatialCollider.name} is not set as trigger!");
            return false;
        }
        
        return true;
    }
    
    private void ValidateEnemySystems(CollisionDetectionMode mode)
    {
        var enemies = FindObjectsOfType<BaseEnemy>();
        
        bool allWorking = true;
        string notes = $"Found {enemies.Length} BaseEnemy instances";
        
        foreach (var enemy in enemies)
        {
            if (!ValidateEnemyCollision(enemy))
            {
                allWorking = false;
                break;
            }
        }
        
        AddValidationResult("Enemy Systems", mode, allWorking, notes);
    }
    
    private bool ValidateEnemyCollision(BaseEnemy enemy)
    {
        var spatialCollider = enemy.GetComponent<SpatialCollider>();
        if (spatialCollider == null)
        {
            Debug.LogWarning($"[CollisionValidator] Enemy {enemy.name} missing SpatialCollider!");
            return false;
        }
        
        // Check if enemy implements ISpatialCollisionHandler
        if (!(enemy is ISpatialCollisionHandler))
        {
            Debug.LogWarning($"[CollisionValidator] Enemy {enemy.name} doesn't implement ISpatialCollisionHandler!");
            return false;
        }
        
        return true;
    }
    
    private void ValidateTriggerSystems(CollisionDetectionMode mode)
    {
        var shopTriggers = FindObjectsOfType<ShopTrigger>();
        var chestComponents = FindObjectsOfType<ChestComponent>();
        var triggerAnimations = FindObjectsOfType<TriggerScaleAnimation>();
        
        bool allWorking = true;
        string notes = $"Found {shopTriggers.Length} ShopTriggers, {chestComponents.Length} ChestComponents, " +
                      $"{triggerAnimations.Length} TriggerScaleAnimations";
        
        // Validate all trigger systems
        foreach (var trigger in shopTriggers)
        {
            if (!ValidateTriggerComponent(trigger.GetComponent<SpatialCollider>()))
            {
                allWorking = false;
                break;
            }
        }
        
        AddValidationResult("Trigger Systems", mode, allWorking, notes);
    }
    
    private bool ValidateTriggerComponent(SpatialCollider spatialCollider)
    {
        if (spatialCollider == null) return false;
        
        // Triggers should be set as triggers
        if (!spatialCollider.IsTrigger)
        {
            Debug.LogWarning($"[CollisionValidator] Trigger {spatialCollider.name} is not set as trigger!");
            return false;
        }
        
        return true;
    }
    
    private void ValidatePlayerSystems(CollisionDetectionMode mode)
    {
        var playerColliders = FindObjectsOfType<SpatialCollider>()
            .Where(sc => sc.Layer == CollisionLayers.Player).ToArray();
        
        bool allWorking = playerColliders.Length > 0;
        string notes = $"Found {playerColliders.Length} Player SpatialColliders";
        
        foreach (var collider in playerColliders)
        {
            if (!ValidatePlayerCollider(collider))
            {
                allWorking = false;
                break;
            }
        }
        
        AddValidationResult("Player Systems", mode, allWorking, notes);
    }
    
    private bool ValidatePlayerCollider(SpatialCollider spatialCollider)
    {
        // Player should not be a trigger (solid collision)
        if (spatialCollider.IsTrigger)
        {
            Debug.LogWarning($"[CollisionValidator] Player collider {spatialCollider.name} should not be a trigger!");
            return false;
        }
        
        return true;
    }
    
    private void AddValidationResult(string systemName, CollisionDetectionMode mode, bool working, string notes)
    {
        var existing = validationResults.FirstOrDefault(r => r.systemName == systemName);
        if (existing != null)
        {
            if (mode == CollisionDetectionMode.Custom)
                existing.customModeWorking = working;
            else
                existing.physics2DModeWorking = working;
            
            existing.notes = notes;
        }
        else
        {
            var result = new ValidationResult(systemName, false, false, notes);
            if (mode == CollisionDetectionMode.Custom)
                result.customModeWorking = working;
            else
                result.physics2DModeWorking = working;
            
            validationResults.Add(result);
        }
    }
    
    private void GenerateValidationReport()
    {
        Debug.Log("=== COLLISION SYSTEM VALIDATION REPORT ===");
        
        bool allSystemsWorking = true;
        foreach (var result in validationResults)
        {
            string status = "";
            if (result.customModeWorking && result.physics2DModeWorking)
                status = "✓ PASS";
            else if (result.customModeWorking || result.physics2DModeWorking)
                status = "⚠ PARTIAL";
            else
            {
                status = "✗ FAIL";
                allSystemsWorking = false;
            }
            
            Debug.Log($"{status} {result.systemName}: Custom={result.customModeWorking}, Physics2D={result.physics2DModeWorking}");
            if (!string.IsNullOrEmpty(result.notes))
                Debug.Log($"    Notes: {result.notes}");
        }
        
        Debug.Log($"=== OVERALL STATUS: {(allSystemsWorking ? "ALL SYSTEMS WORKING" : "ISSUES DETECTED")} ===");
    }
}

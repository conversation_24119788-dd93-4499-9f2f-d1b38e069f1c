using UnityEngine;
using System.Collections;
using Sirenix.OdinInspector;

/// <summary>
/// Burst ranged attack strategy that fires multiple projectiles in quick succession.
/// Some projectiles target directly while others have random spread for area denial.
/// Perfect for creating chaotic, overwhelming ranged attacks.
/// </summary>
[System.Serializable]
public class BurstRangedAttackStrategy : IAttackStrategy
{
    [Title("Burst Configuration")]
    [SerializeField, Range(3, 10)]
    [Tooltip("Total number of projectiles to fire in the burst")]
    private int totalProjectiles = 6;
    
    [SerializeField, Range(1, 5)]
    [Tooltip("Number of projectiles that target directly (rest will be scattered)")]
    private int directProjectiles = 3;
    
    [SerializeField, Range(0.05f, 0.5f)]
    [Tooltip("Delay between each projectile in seconds")]
    private float projectileDelay = 0.1f;
    
    [Title("Spread Configuration")]
    [SerializeField, Range(5f, 30f)]
    [Tooltip("Minimum random angle deviation for scattered projectiles")]
    private float minSpreadAngle = 5f;
    
    [SerializeField, Range(10f, 45f)]
    [Tooltip("Maximum random angle deviation for scattered projectiles")]
    private float maxSpreadAngle = 15f;
    
    [Title("Basic Attack Settings")]
    [SerializeField] private float damage = 15f;
    [SerializeField] private float attackRange = 8.0f;
    [SerializeField] private float attackCooldown = 3.0f;
    [SerializeField] private CollisionLayers projectileLayer = CollisionLayers.EnemyProjectile;
    [SerializeField] private float projectileSpeed = 10f;
    [SerializeField] private GameObject projectilePrefab;
    [SerializeField] private Transform spawnPoint;
    [SerializeField] private string animationName = "burst_attack";
    [SerializeField] private bool requiresAnimation = true;
    
#if UNITY_EDITOR
    [SerializeField, Tooltip("Enable debug logs for BurstRangedAttackStrategy (Editor only).")]
    private bool enableDebugLogs = false;
#endif
    
    // Modifier system
    private AttackModifierCollection modifiers = new AttackModifierCollection();
    
    // Cooldown management
    private float lastAttackTime = -999f;
    
    // Animation event handling
    private Transform currentAttacker;
    private Transform currentTarget;
    private System.Action attackCompletionCallback;
    private bool burstFired = false;
    
    // Component caching
    private SpriteAnimator cachedSpriteAnimator;
    private EnemyAnimationController cachedAnimationController;
    
    // Coroutine management
    private MonoBehaviour coroutineRunner;
    private Coroutine burstCoroutine;
    
    #region IAttackStrategy Implementation
    
    public float Damage 
    { 
        get => (float)modifiers.CalculateDamage(damage).finalDamage;
        set => damage = value;
    }
    
    public CollisionLayers ProjectileLayer
    {
        get => projectileLayer;
        set => projectileLayer = value;
    }
    public float AttackRange => attackRange;
    public float AttackCooldown => modifiers.GetModifiedCooldown(attackCooldown);
    
    public bool IsOnCooldown => Time.time - lastAttackTime < AttackCooldown;
    public float CooldownRemaining => Mathf.Max(0f, AttackCooldown - (Time.time - lastAttackTime));
    public string AttackAnimationName => animationName;
    public bool RequiresAnimation => requiresAnimation;
    
    public bool CanAttack(Transform target, float distanceToTarget)
    {
        return target != null && 
               !IsOnCooldown && 
               distanceToTarget <= attackRange && 
               projectilePrefab != null;
    }
    
    public void ExecuteAttackWithAnimation(Transform attacker, Transform target, System.Action onComplete)
    {
        if (!CanAttack(target, Vector3.Distance(attacker.position, target.position)))
            return;
            
        // Update cooldown
        lastAttackTime = Time.time;
        
        // Store references for animation events
        currentAttacker = attacker;
        currentTarget = target;
        attackCompletionCallback = onComplete;
        burstFired = false;
        
        // Set up coroutine runner
        coroutineRunner = attacker.GetComponent<MonoBehaviour>();
        
        if (requiresAnimation && cachedSpriteAnimator != null)
        {
            // Set up animation event listeners
            cachedSpriteAnimator.OnAnimationEvent += OnAnimationFrameEvent;
            cachedSpriteAnimator.OnAnimationCompleted += OnAnimationCompleted;
            
            // Play attack animation
            if (cachedSpriteAnimator.HasAnimation(animationName))
            {
                // Apply attack speed modifier to animation
                float attackSpeedBonus = modifiers.GetAttackSpeedBonus();
                float attackSpeedMultiplier = 1f + attackSpeedBonus;
                if (attackSpeedMultiplier != 1f)
                {
                    cachedSpriteAnimator.SetTemporarySpeedMultiplier(attackSpeedMultiplier);
                }
                
                cachedSpriteAnimator.PlayForced(animationName);
                
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"[BurstRangedAttack] {attacker.name}: Playing burst attack animation '{animationName}'");
#endif
            }
            else
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"[BurstRangedAttack] {attacker.name}: Animation '{animationName}' not found, executing immediate burst");
#endif
                
                // No animation available, execute immediately
                StartBurstAttack(attacker, target);
                onComplete?.Invoke();
            }
        }
        else
        {
#if UNITY_EDITOR
            if (enableDebugLogs)
                Debug.Log($"[BurstRangedAttack] {attacker.name}: No animation required, executing immediate burst");
#endif
            
            // No animation required, execute immediately
            StartBurstAttack(attacker, target);
            onComplete?.Invoke();
        }
    }
    
    public void ExecuteAttack(Transform attacker, Transform target)
    {
        // Legacy method - calls new method without completion callback
        ExecuteAttackWithAnimation(attacker, target, null);
    }
    
    public void StartCooldown()
    {
        lastAttackTime = Time.time;
    }
    
    public void OnSpawnFromPool()
    {
        // Reset state when spawned from pool
        lastAttackTime = -999f;
        CleanupBurstState();
    }
    
    public void OnReturnToPool()
    {
        // Clean up when returned to pool
        CleanupBurstState();
    }
    
    public void SetupComponents(SpriteAnimator spriteAnimator, EnemyAnimationController animationController)
    {
        cachedSpriteAnimator = spriteAnimator;
        cachedAnimationController = animationController;
    }
    
    /// <summary>
    /// Cancel the currently executing attack.
    /// Stops animation, cleans up events, and restores EnemyAnimationController.
    /// </summary>
    public void CancelAttack()
    {
        // Only cancel if we have an active attack in progress
        if (currentAttacker == null || attackCompletionCallback == null)
        {
            return;
        }
        
#if UNITY_EDITOR
        if (enableDebugLogs)
            Debug.Log($"[BurstRangedAttack] {currentAttacker.name}: Cancelling burst attack due to target out of range");
#endif
        
        // Stop current animation and restore EnemyAnimationController
        if (cachedSpriteAnimator != null)
        {
            // Unsubscribe from events to prevent callbacks
            cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
            cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
            
            // Clear temporary speed override
            cachedSpriteAnimator.ClearTemporarySpeedMultiplier();
            
            // Stop current animation (let EnemyAnimationController take over)
            cachedSpriteAnimator.Stop();
        }
        
        // Re-enable EnemyAnimationController immediately
        if (cachedAnimationController != null)
        {
            cachedAnimationController.enabled = true;
#if UNITY_EDITOR
            if (enableDebugLogs)
                Debug.Log($"[BurstRangedAttack] {currentAttacker.name}: Re-enabled EnemyAnimationController after cancel");
#endif
        }
        
        // DO NOT invoke the completion callback since this is a cancellation
        // The movement system should handle the cancellation appropriately
        
        // Clean up burst state (includes stopping coroutines)
        CleanupBurstState();
    }
    
    public void UpdateModifiers(System.Collections.Generic.List<AttackModifier> externalModifiers)
    {
        modifiers.ClearModifiers();
        
        if (externalModifiers != null)
        {
            foreach (var modifier in externalModifiers)
            {
                modifiers.AddModifier(modifier);
            }
        }
    }
    
    #endregion
    
    #region Burst Attack Implementation
    
    private void StartBurstAttack(Transform attacker, Transform target)
    {
        if (coroutineRunner != null)
        {
            // Stop any existing burst
            if (burstCoroutine != null)
            {
                coroutineRunner.StopCoroutine(burstCoroutine);
            }
            
            // Start new burst coroutine
            burstCoroutine = coroutineRunner.StartCoroutine(ExecuteBurstSequence(attacker, target));
        }
        else
        {
            // No coroutine runner available, fire all at once as fallback
            SpawnAllProjectiles(attacker, target);
        }
    }
    
    private IEnumerator ExecuteBurstSequence(Transform attacker, Transform target)
    {
        // Calculate damage once for all projectiles
        CriticalHitResult damageResult = modifiers.CalculateDamage(damage);
        
        // Calculate modified projectile speed
        float modifiedSpeed = modifiers.GetModifiedProjectileSpeed(projectileSpeed);
        
        var poolManager = PoolManager.Instance;
        if (poolManager == null)
        {
#if UNITY_EDITOR
            if (enableDebugLogs)
                Debug.LogWarning($"[BurstRangedAttack] {attacker.name}: PoolManager not found!");
#endif
            yield break;
        }
        
        // Calculate total projectiles including chunk buffs
        int chunkBuffProjectileBonus = modifiers.GetProjectileCount() - 1; // -1 because GetProjectileCount() includes base
        int actualTotalProjectiles = Mathf.Min(totalProjectiles + chunkBuffProjectileBonus, 10); // Cap at 10 for burst attacks
        
        // Scale direct projectiles proportionally to maintain balance
        int actualDirectProjectiles = Mathf.Min(directProjectiles + (chunkBuffProjectileBonus / 2), actualTotalProjectiles);
        
#if UNITY_EDITOR
        if (enableDebugLogs)
        {
            Debug.Log($"[BurstRangedAttack] {attacker.name}: Starting burst - {actualTotalProjectiles} projectiles ({actualDirectProjectiles} direct, {actualTotalProjectiles - actualDirectProjectiles} scattered) [Base: {totalProjectiles}, ChunkBonus: +{chunkBuffProjectileBonus}]");
        }
#endif
        
        // Fire projectiles with delays
        for (int i = 0; i < actualTotalProjectiles; i++)
        {
            // Check if we should abort (attacker destroyed, etc.)
            if (attacker == null || target == null)
                yield break;
                
            // Calculate fresh spawn position and direction for each projectile (fixes moving enemy issue)
            Vector3 currentSpawnPosition = spawnPoint != null ? spawnPoint.position : attacker.position;
            Vector3 currentBaseDirection = (target.position - currentSpawnPosition).normalized;
            
            bool isDirect = i < actualDirectProjectiles;
            Vector3 direction = CalculateProjectileDirection(currentBaseDirection, isDirect, i);
            
            SpawnSingleProjectile(currentSpawnPosition, direction, damageResult, modifiedSpeed, poolManager, isDirect);
            
            // Wait before next projectile (except for the last one)
            if (i < actualTotalProjectiles - 1)
            {
                yield return new WaitForSeconds(projectileDelay);
            }
        }
        
#if UNITY_EDITOR
        if (enableDebugLogs)
        {
            Debug.Log($"[BurstRangedAttack] {attacker.name}: Burst sequence completed");
        }
#endif
        
        burstCoroutine = null;
    }
    
    private Vector3 CalculateProjectileDirection(Vector3 baseDirection, bool isDirect, int projectileIndex)
    {
        if (isDirect)
        {
            // Direct projectiles go straight to target with minimal spread
            float directSpread = 2f; // Very small spread for direct shots
            float angle = Random.Range(-directSpread, directSpread);
            return Quaternion.AngleAxis(angle, Vector3.forward) * baseDirection;
        }
        else
        {
            // Scattered projectiles have random angle deviation
            float angle = Random.Range(-maxSpreadAngle, maxSpreadAngle);
            
            // Ensure minimum deviation from direct path
            if (Mathf.Abs(angle) < minSpreadAngle)
            {
                angle = angle >= 0 ? minSpreadAngle : -minSpreadAngle;
            }
            
            return Quaternion.AngleAxis(angle, Vector3.forward) * baseDirection;
        }
    }
    
    private void SpawnSingleProjectile(Vector3 spawnPosition, Vector3 direction, CriticalHitResult damageResult, float speed, PoolManager poolManager, bool isDirect)
    {
        GameObject projectileObj = poolManager.Spawn(projectilePrefab, spawnPosition, Quaternion.identity);
        
        if (projectileObj != null)
        {
            if (poolManager.GetCachedComponent<Projectile>(projectileObj, out var projectile))
            {
                projectile.Initialize(
                    spawnPosition,
                    direction,
                    (float)damageResult.finalDamage,
                    projectileLayer,
                    speed,
                    5f // lifetime
                );
                
#if UNITY_EDITOR
                if (enableDebugLogs)
                {
                    string typeStr = isDirect ? "direct" : "scattered";
                    Debug.Log($"[BurstRangedAttack] Spawned {typeStr} projectile with damage {damageResult.finalDamage}");
                }
#endif
            }
        }
    }
    
    private void SpawnAllProjectiles(Transform attacker, Transform target)
    {
        // Fallback method - spawn all projectiles at once
        CriticalHitResult damageResult = modifiers.CalculateDamage(damage);
        float modifiedSpeed = modifiers.GetModifiedProjectileSpeed(projectileSpeed);
        
        // Calculate total projectiles including chunk buffs (same logic as coroutine)
        int chunkBuffProjectileBonus = modifiers.GetProjectileCount() - 1;
        int actualTotalProjectiles = Mathf.Min(totalProjectiles + chunkBuffProjectileBonus, 10);
        int actualDirectProjectiles = Mathf.Min(directProjectiles + (chunkBuffProjectileBonus / 2), actualTotalProjectiles);
        
        var poolManager = PoolManager.Instance;
        if (poolManager == null) return;
        
        for (int i = 0; i < actualTotalProjectiles; i++)
        {
            // Calculate fresh spawn position and direction for each projectile (fixes moving enemy issue)
            Vector3 currentSpawnPosition = spawnPoint != null ? spawnPoint.position : attacker.position;
            Vector3 currentBaseDirection = (target.position - currentSpawnPosition).normalized;
            
            bool isDirect = i < actualDirectProjectiles;
            Vector3 direction = CalculateProjectileDirection(currentBaseDirection, isDirect, i);
            SpawnSingleProjectile(currentSpawnPosition, direction, damageResult, modifiedSpeed, poolManager, isDirect);
        }
    }
    
    #endregion
    
    #region Animation Event Handling
    
    private void OnAnimationFrameEvent(string eventName, string parameter)
    {
        // Check for burst trigger events
        if (!burstFired && currentAttacker != null && currentTarget != null)
        {
            string lowerEventName = eventName.ToLower();
            if (lowerEventName.Contains("burst") || lowerEventName.Contains("shoot") || 
                lowerEventName.Contains("fire") || lowerEventName.Contains("projectile"))
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"[BurstRangedAttack] {currentAttacker.name}: Starting burst from animation event '{eventName}'");
#endif
                
                StartBurstAttack(currentAttacker, currentTarget);
                burstFired = true;
            }
#if UNITY_EDITOR
            else if (enableDebugLogs)
            {
                Debug.Log($"[BurstRangedAttack] {currentAttacker?.name}: Animation event '{eventName}' ignored - not a burst trigger");
            }
#endif
        }
    }
    
    private void OnAnimationCompleted(string animationName)
    {
        if (animationName == this.animationName)
        {
            // Handle completion fallback if no frame event was triggered
            if (!burstFired && currentAttacker != null && currentTarget != null)
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"[BurstRangedAttack] {currentAttacker.name}: Animation completed without frame event - using fallback burst");
#endif
                StartBurstAttack(currentAttacker, currentTarget);
            }
            
            // Notify completion and cleanup
            attackCompletionCallback?.Invoke();
            CleanupBurstState();
        }
    }
    
    private void CleanupBurstState()
    {
        // Unsubscribe from events
        if (cachedSpriteAnimator != null)
        {
            cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
            cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
            cachedSpriteAnimator.ClearTemporarySpeedMultiplier();
        }
        
        // Stop any running coroutine
        if (burstCoroutine != null && coroutineRunner != null)
        {
            coroutineRunner.StopCoroutine(burstCoroutine);
            burstCoroutine = null;
        }
        
        // Clear references
        currentAttacker = null;
        currentTarget = null;
        attackCompletionCallback = null;
        burstFired = false;
        coroutineRunner = null;
    }
    
    #endregion
    
    #region Validation & Debug
    
    private void OnValidate()
    {
        // Ensure direct projectiles don't exceed total
        directProjectiles = Mathf.Min(directProjectiles, totalProjectiles);
        
        // Ensure spread angles are logical
        maxSpreadAngle = Mathf.Max(maxSpreadAngle, minSpreadAngle);
    }
    
    #endregion
}
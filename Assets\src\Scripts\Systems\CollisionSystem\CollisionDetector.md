using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Enhanced collision detector using Unity Physics2D with optimized hit processing modes
/// Supports zero-GC collision detection with pre-allocated arrays and ISpawnable pooling
/// </summary>
public class CollisionDetector : MonoBehaviour, ISpawnable
{
    [Title("Hit Processing")]
    [SerializeField, Tooltip("FirstHitOnly: Process only first collision (projectiles, triggers)\nAllHits: Process all collisions (area effects)\nMaxHits: Process up to max limit (chain spells)")]
    private HitProcessingMode hitProcessingMode = HitProcessingMode.FirstHitOnly;
    
    [SerializeField, Range(1, 50), ShowIf("hitProcessingMode", HitProcessingMode.MaxHits)]
    [Tooltip("Maximum number of hits to process when using MaxHits mode")]
    private int maxHitsToProcess = 10;
    
    [Title("Detection Settings")]
    [SerializeField, <PERSON>lt<PERSON>("Which layers this detector can collide with")]
    private LayerMask targetLayerMask = -1;
    
    [Serial<PERSON><PERSON><PERSON>, Toolt<PERSON>("Include trigger colliders in collision detection")]
    private bool includeTriggers = true;
    
    [SerializeField, Range(0.1f, 10f), Tooltip("Detection radius in world units")]
    private float detectionRadius = 1f;
    
    [Title("Debug")]
    [SerializeField, Tooltip("Show collision radius and hit detection in scene view")]
    private bool showDetectionGizmos = false;
    
    [SerializeField, ShowIf("showDetectionGizmos"), Tooltip("Color for debug gizmos")]
    private Color gizmoColor = Color.yellow;
    
    // Pre-allocated arrays for zero-GC collision detection
    private static readonly Collider2D[] overlapResults = new Collider2D[30];
    private static readonly RaycastHit2D[] castResults = new RaycastHit2D[30];
    
    // Cached contact filter for performance
    private ContactFilter2D cachedFilter;
    private LayerMask originalLayerMask; // Store for OnDespawn reset
    
    // State variables requiring pool reset
    private bool hasBeenReturned = false;
    private float lastDetectionTime = 0f;
    private float detectionCooldownRemaining = 0f;
    private readonly System.Collections.Generic.HashSet<int> detectedObjects = new System.Collections.Generic.HashSet<int>();
    private Transform lastDetectedTarget;
    private bool isDetectionActive = true;
    
    void Start()
    {
        // Initialize cached contact filter
        cachedFilter = new ContactFilter2D();
        cachedFilter.SetLayerMask(targetLayerMask);
        cachedFilter.useTriggers = includeTriggers;
        originalLayerMask = targetLayerMask; // Cache original for reset
    }
    
    /// <summary>
    /// Detect collisions at this object's position with default radius
    /// </summary>
    public bool DetectCollisions()
    {
        return DetectCollisions(transform.position, detectionRadius);
    }
    
    /// <summary>
    /// Detect collisions at specified position and radius
    /// Returns true if any collisions were detected and processed
    /// </summary>
    public bool DetectCollisions(Vector2 position, float radius)
    {
        if (hasBeenReturned || !isDetectionActive) return false;
        
        // Use Physics2D.OverlapCircleNonAlloc for zero-GC detection
        int hits = Physics2D.OverlapCircleNonAlloc(position, radius, cachedFilter, overlapResults);
        
        return ProcessDetectionResults(hits);
    }
    
    /// <summary>
    /// Detect collisions using circle cast for moving objects (like projectiles)
    /// </summary>
    public bool DetectCollisionsWithMovement(Vector2 direction, float distance)
    {
        if (hasBeenReturned || !isDetectionActive) return false;
        
        // Use CircleCast for moving collision detection
        int hits = Physics2D.CircleCast(
            transform.position, 
            detectionRadius, 
            direction.normalized, 
            cachedFilter, 
            castResults, 
            distance
        );
        
        return ProcessCastResults(hits);
    }
    
    private bool ProcessDetectionResults(int hitCount)
    {
        if (hitCount == 0) return false;
        
        if (hitProcessingMode == HitProcessingMode.FirstHitOnly)
        {
            // Early exit optimization - process only first hit
            int targetID = overlapResults[0].GetInstanceID();
            if (detectedObjects.Contains(targetID)) return false; // Already processed
            
            detectedObjects.Add(targetID);
            lastDetectedTarget = overlapResults[0].transform;
            lastDetectionTime = Time.time;
            OnCollisionDetected(overlapResults[0]);
            return true; // Early exit - performance boost
        }
        
        bool anyDetected = false;
        int processCount = hitProcessingMode == HitProcessingMode.MaxHits ? 
            Mathf.Min(hitCount, maxHitsToProcess) : hitCount;
            
        for (int i = 0; i < processCount; i++)
        {
            int targetID = overlapResults[i].GetInstanceID();
            if (!detectedObjects.Contains(targetID))
            {
                detectedObjects.Add(targetID);
                OnCollisionDetected(overlapResults[i]);
                anyDetected = true;
            }
        }
        
        if (anyDetected)
        {
            lastDetectionTime = Time.time;
        }
        
        return anyDetected;
    }
    
    private bool ProcessCastResults(int hitCount)
    {
        if (hitCount == 0) return false;
        
        if (hitProcessingMode == HitProcessingMode.FirstHitOnly)
        {
            // Early exit optimization for moving objects
            OnCastHit(castResults[0]);
            return true; // Early exit
        }
        
        int processCount = hitProcessingMode == HitProcessingMode.MaxHits ? 
            Mathf.Min(hitCount, maxHitsToProcess) : hitCount;
            
        for (int i = 0; i < processCount; i++)
        {
            OnCastHit(castResults[i]);
        }
        
        return true;
    }
    
    /// <summary>
    /// Override this method in derived classes for specific collision handling
    /// Called when overlap detection finds a collision
    /// </summary>
    protected virtual void OnCollisionDetected(Collider2D collider)
    {
        // Default implementation - can be overridden
        if (Application.isPlaying && showDetectionGizmos)
        {
            Debug.Log($"CollisionDetector: Detected collision with {collider.name} at {Time.time}");
        }
    }
    
    /// <summary>
    /// Override this method in derived classes for cast-based collision handling
    /// Called when cast detection finds a collision
    /// </summary>
    protected virtual void OnCastHit(RaycastHit2D hit)
    {
        // Default implementation - can be overridden
        if (Application.isPlaying && showDetectionGizmos)
        {
            Debug.Log($"CollisionDetector: Cast hit {hit.collider.name} at {hit.point}");
        }
    }
    
    /// <summary>
    /// Update the layer mask at runtime
    /// </summary>
    public void SetLayerMask(LayerMask newMask)
    {
        targetLayerMask = newMask;
        cachedFilter.SetLayerMask(newMask);
    }
    
    /// <summary>
    /// Get current detection status
    /// </summary>
    public bool IsDetectionActive => isDetectionActive && !hasBeenReturned;
    
    // ISpawnable implementation with complete state reset
    public void OnSpawn()
    {
        // Reset detection state for new spawn
        hasBeenReturned = false;
        isDetectionActive = true;
        lastDetectionTime = 0f;
        detectionCooldownRemaining = 0f;
        lastDetectedTarget = null;
        
        // Collections are already cleared in OnDespawn
        // Don't clear again to avoid unnecessary allocation
    }
    
    public void OnDespawn()
    {
        // CRITICAL: Complete state reset for pool reuse
        hasBeenReturned = false;
        lastDetectionTime = 0f;
        detectionCooldownRemaining = 0f;
        isDetectionActive = false;
        
        // Clear collections - prevents memory buildup over time
        detectedObjects.Clear();
        
        // Clear pre-allocated arrays using Array.Clear for performance
        System.Array.Clear(overlapResults, 0, overlapResults.Length);
        System.Array.Clear(castResults, 0, castResults.Length);
        
        // Reset ContactFilter2D if modified at runtime
        if (cachedFilter.layerMask != originalLayerMask)
        {
            cachedFilter.SetLayerMask(originalLayerMask);
        }
        
        // Clear object references - prevents memory leaks
        lastDetectedTarget = null;
        
        // Reset inspector values if modified at runtime
        if (hitProcessingMode != HitProcessingMode.FirstHitOnly)
        {
            hitProcessingMode = HitProcessingMode.FirstHitOnly;
        }
    }
    
    // Debug visualization
    void OnDrawGizmosSelected()
    {
        if (!showDetectionGizmos) return;
        
        // Main collision radius
        Gizmos.color = gizmoColor;
        Gizmos.DrawWireCircle(transform.position, detectionRadius);
        
        // Hit processing mode indicator
        switch (hitProcessingMode)
        {
            case HitProcessingMode.FirstHitOnly:
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(transform.position + Vector3.up * (detectionRadius + 0.5f), Vector3.one * 0.3f);
                break;
                
            case HitProcessingMode.AllHits:
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(transform.position + Vector3.up * (detectionRadius + 0.5f), 0.2f);
                break;
                
            case HitProcessingMode.MaxHits:
                Gizmos.color = Color.blue;
                // Draw max hits indicator
                for (int i = 0; i < Mathf.Min(maxHitsToProcess, 10); i++)
                {
                    Vector3 pos = transform.position + Vector3.up * (detectionRadius + 0.5f) + 
                                 Vector3.right * (i * 0.2f - maxHitsToProcess * 0.1f);
                    Gizmos.DrawWireCube(pos, Vector3.one * 0.1f);
                }
                break;
        }
    }
    
    void OnDrawGizmos()
    {
        if (!showDetectionGizmos) return;
        
        // Faded detection area
        Gizmos.color = new Color(gizmoColor.r, gizmoColor.g, gizmoColor.b, 0.3f);
        Gizmos.DrawSphere(transform.position, detectionRadius);
    }
    
    // Runtime debug info in Game view
    void OnGUI()
    {
        if (!showDetectionGizmos || !Application.isPlaying) return;
        
        Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position);
        if (screenPos.z > 0) // Only show if in front of camera
        {
            GUI.Label(new Rect(screenPos.x, Screen.height - screenPos.y, 200, 60), 
                $"Mode: {hitProcessingMode}\nRadius: {detectionRadius:F1}\nTargets: {(hitProcessingMode == HitProcessingMode.MaxHits ? maxHitsToProcess.ToString() : "∞")}");
        }
    }
}
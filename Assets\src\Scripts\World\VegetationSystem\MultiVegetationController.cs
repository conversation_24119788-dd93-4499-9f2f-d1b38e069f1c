using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using PrimeTween;

/// <summary>
/// Controls multiple child SpriteRenderers for varied vegetation display.
/// Each vegetation prefab can show multiple different sprites simultaneously.
/// </summary>
[RequireComponent(typeof(VegetationInstance))]
public class MultiVegetationController : MonoBehaviour, ISpawnable
{
    [Title("Multi-Vegetation Controller")]
    [SerializeField, Range(1, 5)]
    [Tooltip("Maximum number of child sprites that can be active simultaneously")]
    private int maxActiveSprites = 3;
    
    [SerializeField, Range(0f, 1f)]
    [Tooltip("Probability that each additional sprite beyond the first will be activated")]
    private float additionalSpriteProbability = 0.6f;
    
    [SerializeField]
    [Tooltip("Child GameObjects containing SpriteRenderer components")]
    private Transform[] childSpriteObjects;
    
    [Title("Material Animation Settings")]
    [SerializeField]
    [Tooltip("Material property name to animate (e.g., '_<PERSON>', '_DissolveProgress')")]
    private string materialPropertyName = "_Alpha";
    
    [SerializeField]
    [Tooltip("Start value for the material property")]
    private float startValue = 1f;
    
    [SerializeField]
    [Tooltip("End value for the material property")]
    private float endValue = 0f;
    
    [SerializeField]
    [Tooltip("Animation duration in seconds")]
    private float animationDuration = 2f;
    
    [SerializeField]
    [Tooltip("Easing function for the animation")]
    private Ease animationEase = Ease.OutQuad;
    
    // Cached component references for performance
    private SpriteRenderer[] childSpriteRenderers;
    private YSortingController[] childYSortingControllers;
    private VegetationInstance vegetationInstance;
    
    // Individual material instances system
    private Material[] childMaterialInstances;
    private Material originalMaterial;
    
    // Runtime state
    private int currentActiveCount = 0;
    private List<int> activeIndices = new List<int>();
    
    // Corruption state to prevent redundant breach detection
    private bool isCorrupted = false;
    private SpatialCollider spatialCollider;
    
    void Awake()
    {
        vegetationInstance = GetComponent<VegetationInstance>();
        spatialCollider = GetComponent<SpatialCollider>();
        CacheChildComponents();
        CreateIndividualMaterialInstances();
    }
    
    /// <summary>
    /// Cache all child SpriteRenderer and YSortingController components to avoid GetComponent calls
    /// </summary>
    private void CacheChildComponents()
    {
        if (childSpriteObjects == null || childSpriteObjects.Length == 0)
        {
            Debug.LogError($"MultiVegetationController on {gameObject.name}: No child sprite objects assigned!");
            return;
        }

        childSpriteRenderers = new SpriteRenderer[childSpriteObjects.Length];
        childYSortingControllers = new YSortingController[childSpriteObjects.Length];

        for (int i = 0; i < childSpriteObjects.Length; i++)
        {
            if (childSpriteObjects[i] != null)
            {
                // Cache SpriteRenderer (required)
                childSpriteRenderers[i] = childSpriteObjects[i].GetComponent<SpriteRenderer>();
                if (childSpriteRenderers[i] == null)
                {
                    Debug.LogError($"MultiVegetationController on {gameObject.name}: Child object {i} ({childSpriteObjects[i].name}) has no SpriteRenderer component!");
                }

                // Cache YSortingController (optional but recommended)
                childYSortingControllers[i] = childSpriteObjects[i].GetComponent<YSortingController>();
                if (childYSortingControllers[i] == null)
                {
                    Debug.LogWarning($"MultiVegetationController on {gameObject.name}: Child object {i} ({childSpriteObjects[i].name}) has no YSortingController component!");
                }
            }
        }
    }
    
    /// <summary>
    /// Creates individual material instances for each child sprite renderer
    /// </summary>
    private void CreateIndividualMaterialInstances()
    {
        if (childSpriteRenderers == null || childSpriteRenderers.Length == 0)
            return;
            
        childMaterialInstances = new Material[childSpriteRenderers.Length];
        
        for (int i = 0; i < childSpriteRenderers.Length; i++)
        {
            if (childSpriteRenderers[i] != null && childSpriteRenderers[i].sharedMaterial != null)
            {
                // Store original material reference (same for all)
                if (originalMaterial == null)
                {
                    originalMaterial = childSpriteRenderers[i].sharedMaterial;
                }
                
                // Create individual material instance for this child
                childMaterialInstances[i] = new Material(childSpriteRenderers[i].sharedMaterial);
                childSpriteRenderers[i].material = childMaterialInstances[i];
            }
        }
    }
    
    /// <summary>
    /// Ensures active sprites have their material instances assigned
    /// </summary>
    private void EnsureMaterialInstancesForActiveSprites()
    {
        if (childMaterialInstances == null || childSpriteRenderers == null)
            return;
            
        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childSpriteRenderers.Length && 
                childIndex < childMaterialInstances.Length &&
                childSpriteRenderers[childIndex] != null &&
                childMaterialInstances[childIndex] != null)
            {
                childSpriteRenderers[childIndex].material = childMaterialInstances[childIndex];
            }
        }
    }
    
    /// <summary>
    /// Restores original material to all child sprite renderers
    /// </summary>
    private void RestoreOriginalMaterial()
    {
        if (originalMaterial == null || childSpriteRenderers == null)
            return;
            
        for (int i = 0; i < childSpriteRenderers.Length; i++)
        {
            if (childSpriteRenderers[i] != null)
            {
                childSpriteRenderers[i].material = originalMaterial;
            }
        }
    }
    
    /// <summary>
    /// Cleans up all individual material instances
    /// </summary>
    private void CleanupMaterialInstances()
    {
        if (childMaterialInstances != null)
        {
            for (int i = 0; i < childMaterialInstances.Length; i++)
            {
                if (childMaterialInstances[i] != null)
                {
                    if (Application.isPlaying)
                    {
                        Destroy(childMaterialInstances[i]);
                    }
                    else
                    {
                        DestroyImmediate(childMaterialInstances[i]);
                    }
                    childMaterialInstances[i] = null;
                }
            }
            childMaterialInstances = null;
        }
    }
    
    /// <summary>
    /// Sets multiple random sprites from the provided array with random variation
    /// </summary>
    /// <param name="sprites">Array of sprites to choose from</param>
    /// <param name="color">Color to apply to all sprites</param>
    /// <param name="flipX">Whether to flip sprites horizontally</param>
    public void SetRandomSprites(Sprite[] sprites, Color color, bool flipX = false)
    {
        if (sprites == null || sprites.Length == 0 || childSpriteRenderers == null)
        {
            ClearAllSprites();
            return;
        }
        
        // Clear all sprites first
        ClearAllSprites();
        
        // Determine how many sprites to activate (at least 1, up to maxActiveSprites)
        int spritesToActivate = DetermineActiveCount();
        
        // Randomly select which child indices to use
        SelectRandomActiveIndices(spritesToActivate);
        
        // Assign random sprites to selected children
        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childSpriteRenderers.Length && childSpriteRenderers[childIndex] != null)
            {
                // Pick a random sprite from the array
                Sprite randomSprite = sprites[Random.Range(0, sprites.Length)];
                
                // Apply sprite, color, and flip
                childSpriteRenderers[childIndex].sprite = randomSprite;
                childSpriteRenderers[childIndex].color = color;
                childSpriteRenderers[childIndex].flipX = flipX;
                childSpriteRenderers[childIndex].enabled = true;
            }
        }
        
        currentActiveCount = activeIndices.Count;
        
        // Ensure active sprites have their material instances
        EnsureMaterialInstancesForActiveSprites();
    }
    
    /// <summary>
    /// Sets the color for all active child sprites
    /// </summary>
    /// <param name="color">Color to apply</param>
    public void SetColor(Color color)
    {
        if (childSpriteRenderers == null) return;
        
        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childSpriteRenderers.Length && childSpriteRenderers[childIndex] != null)
            {
                childSpriteRenderers[childIndex].color = color;
            }
        }
    }
    
    /// <summary>
    /// Sets the flip state for all active child sprites
    /// </summary>
    /// <param name="flipX">Whether to flip horizontally</param>
    public void SetFlip(bool flipX)
    {
        if (childSpriteRenderers == null) return;

        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childSpriteRenderers.Length && childSpriteRenderers[childIndex] != null)
            {
                childSpriteRenderers[childIndex].flipX = flipX;
            }
        }
    }

    /// <summary>
    /// Sets the Y-sorting offset for all active child YSortingControllers
    /// </summary>
    /// <param name="yOffset">Y-offset value for sorting</param>
    public void SetYSortingOffset(float yOffset)
    {
        if (childYSortingControllers == null) return;

        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childYSortingControllers.Length && childYSortingControllers[childIndex] != null)
            {
                childYSortingControllers[childIndex].SetYOffset(yOffset);
            }
        }
    }
    
    /// <summary>
    /// Clears all child sprites and disables them
    /// </summary>
    public void ClearAllSprites()
    {
        if (childSpriteRenderers == null) return;
        
        for (int i = 0; i < childSpriteRenderers.Length; i++)
        {
            if (childSpriteRenderers[i] != null)
            {
                childSpriteRenderers[i].sprite = null;
                childSpriteRenderers[i].color = Color.white;
                childSpriteRenderers[i].flipX = false;
                childSpriteRenderers[i].enabled = false;
            }
        }
        
        activeIndices.Clear();
        currentActiveCount = 0;
        
        // Restore original materials when clearing
        RestoreOriginalMaterial();
    }
    
    /// <summary>
    /// Determines how many sprites should be active based on probability
    /// </summary>
    private int DetermineActiveCount()
    {
        int count = 1; // Always activate at least one sprite
        int maxPossible = Mathf.Min(maxActiveSprites, childSpriteRenderers.Length);
        
        // Roll for additional sprites
        for (int i = 1; i < maxPossible; i++)
        {
            if (Random.value < additionalSpriteProbability)
            {
                count++;
            }
            else
            {
                break; // Stop rolling once we fail a probability check
            }
        }
        
        return count;
    }
    
    /// <summary>
    /// Randomly selects which child indices to activate
    /// </summary>
    private void SelectRandomActiveIndices(int count)
    {
        activeIndices.Clear();
        
        // Create a list of available indices
        List<int> availableIndices = new List<int>();
        for (int i = 0; i < childSpriteRenderers.Length; i++)
        {
            if (childSpriteRenderers[i] != null)
            {
                availableIndices.Add(i);
            }
        }
        
        // Randomly select indices without replacement
        for (int i = 0; i < count && availableIndices.Count > 0; i++)
        {
            int randomIndex = Random.Range(0, availableIndices.Count);
            activeIndices.Add(availableIndices[randomIndex]);
            availableIndices.RemoveAt(randomIndex);
        }
    }
    
    /// <summary>
    /// Gets the number of currently active sprites
    /// </summary>
    public int GetActiveCount()
    {
        return currentActiveCount;
    }
    
    /// <summary>
    /// Gets the total number of available child sprites
    /// </summary>
    public int GetTotalChildCount()
    {
        return childSpriteRenderers?.Length ?? 0;
    }
    
    // ISpawnable implementation for object pooling
    public void OnSpawn()
    {
        ClearAllSprites();
        
        // Reset corruption state for new spawn
        isCorrupted = false;
        if (spatialCollider != null)
        {
            // Restore original vegetation collision layer
            spatialCollider.SetLayer(CollisionLayers.Vegetation);
            spatialCollider.ForceCollisionUpdate();
        }
    }
    
    public void OnDespawn()
    {
        ClearAllSprites();
        
        // Reset corruption state
        isCorrupted = false;
        if (spatialCollider != null)
        {
            // Restore original vegetation collision layer
            spatialCollider.SetLayer(CollisionLayers.Vegetation);
            spatialCollider.ForceCollisionUpdate();
        }
        
        // Reset individual material instances for pool reuse
        if (childMaterialInstances != null && originalMaterial != null)
        {
            for (int i = 0; i < childMaterialInstances.Length; i++)
            {
                if (childMaterialInstances[i] != null)
                {
                    // Reset any animated properties to their defaults
                    childMaterialInstances[i].CopyPropertiesFromMaterial(originalMaterial);
                }
            }
        }
    }
    
    #region Material Property Animation
    
    /// <summary>
    /// Sets a float property on all active child material instances
    /// </summary>
    /// <param name="propertyName">Material property name (e.g., "_Alpha", "_DissolveProgress")</param>
    /// <param name="value">Property value</param>
    public void SetMaterialProperty(string propertyName, float value)
    {
        if (childMaterialInstances == null || string.IsNullOrEmpty(propertyName))
            return;
            
        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childMaterialInstances.Length && childMaterialInstances[childIndex] != null)
            {
                if (childMaterialInstances[childIndex].HasProperty(propertyName))
                {
                    childMaterialInstances[childIndex].SetFloat(propertyName, value);
                }
                else
                {
                    Debug.LogWarning($"[MultiVegetationController] {gameObject.name}: Material does not have property '{propertyName}'. Available properties on material: {childMaterialInstances[childIndex].shader.name}");
                }
            }
        }
    }
    
    /// <summary>
    /// Sets a color property on all active child material instances
    /// </summary>
    /// <param name="propertyName">Material property name (e.g., "_EmissionColor")</param>
    /// <param name="color">Property color</param>
    public void SetMaterialProperty(string propertyName, Color color)
    {
        if (childMaterialInstances == null || string.IsNullOrEmpty(propertyName))
            return;
            
        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childMaterialInstances.Length && childMaterialInstances[childIndex] != null)
            {
                childMaterialInstances[childIndex].SetColor(propertyName, color);
            }
        }
    }
    
    /// <summary>
    /// Animates a float material property using PrimeTween on all active child materials
    /// </summary>
    /// <param name="propertyName">Material property name</param>
    /// <param name="fromValue">Start value</param>
    /// <param name="toValue">Target value</param>
    /// <param name="duration">Animation duration</param>
    /// <param name="ease">Easing function</param>
    /// <returns>Tween handle</returns>
    public Tween AnimateMaterialProperty(string propertyName, float fromValue, float toValue, float duration, Ease ease = Ease.OutQuad)
    {
        if (childMaterialInstances == null || string.IsNullOrEmpty(propertyName))
        {
            Debug.LogWarning($"[MultiVegetationController] {gameObject.name}: Cannot animate material property - material instances or property name is null");
            return default;
        }
        
        // Set initial value on all active materials
        SetMaterialProperty(propertyName, fromValue);
        
        // Animate to target value on all active materials
        return Tween.Custom(fromValue, toValue, duration, 
            value => SetMaterialProperty(propertyName, value), ease);
    }
    
    /// <summary>
    /// Animates a color material property using PrimeTween on all active child materials
    /// </summary>
    /// <param name="propertyName">Material property name</param>
    /// <param name="fromColor">Start color</param>
    /// <param name="toColor">Target color</param>
    /// <param name="duration">Animation duration</param>
    /// <param name="ease">Easing function</param>
    /// <returns>Tween handle</returns>
    public Tween AnimateMaterialProperty(string propertyName, Color fromColor, Color toColor, float duration, Ease ease = Ease.OutQuad)
    {
        if (childMaterialInstances == null || string.IsNullOrEmpty(propertyName))
        {
            Debug.LogWarning($"[MultiVegetationController] {gameObject.name}: Cannot animate material property - material instances or property name is null");
            return default;
        }
        
        // Set initial value on all active materials
        SetMaterialProperty(propertyName, fromColor);
        
        // Animate to target value on all active materials
        return Tween.Custom(fromColor, toColor, duration, 
            color => SetMaterialProperty(propertyName, color), ease);
    }
    
    /// <summary>
    /// Quick fade animation using individual material instances
    /// </summary>
    /// <param name="toAlpha">Target alpha (0-1)</param>
    /// <param name="duration">Animation duration</param>
    /// <param name="ease">Easing function</param>
    /// <returns>Tween handle</returns>
    public Tween FadeToAlpha(float toAlpha, float duration, Ease ease = Ease.OutQuad)
    {
        float currentAlpha = GetCurrentMaterialValue("_Alpha");
        return AnimateMaterialProperty("_Alpha", currentAlpha, toAlpha, duration, ease);
    }
    
    /// <summary>
    /// Quick dissolve animation using individual material instances
    /// </summary>
    /// <param name="toDissolve">Target dissolve progress (0-1)</param>
    /// <param name="duration">Animation duration</param>
    /// <param name="ease">Easing function</param>
    /// <returns>Tween handle</returns>
    public Tween DissolveToProgress(float toDissolve, float duration, Ease ease = Ease.OutQuad)
    {
        float currentDissolve = GetCurrentMaterialValue("_DissolveProgress");
        return AnimateMaterialProperty("_DissolveProgress", currentDissolve, toDissolve, duration, ease);
    }
    
    /// <summary>
    /// Triggers the configured material animation using Inspector settings
    /// OPTIMIZED: Only triggers once per spawn to prevent redundant breach detection overhead
    /// </summary>
    /// <returns>Tween handle or default if animation cannot start</returns>
    public Tween TriggerMaterialAnimation()
    {
        // Early exit if already corrupted - prevents redundant animations and collision overhead
        if (isCorrupted)
        {
            return default;
        }
        
        if (!Application.isPlaying)
        {
            Debug.LogWarning($"[MultiVegetationController] {gameObject.name}: Cannot animate in edit mode");
            return default;
        }
        
        if (string.IsNullOrEmpty(materialPropertyName))
        {
            Debug.LogWarning($"[MultiVegetationController] {gameObject.name}: Material property name is empty");
            return default;
        }
        
        if (childMaterialInstances == null)
        {
            Debug.LogWarning($"[MultiVegetationController] {gameObject.name}: No material instances found");
            return default;
        }
        
        if (activeIndices.Count == 0)
        {
            Debug.LogWarning($"[MultiVegetationController] {gameObject.name}: No active sprites found - ensure vegetation is spawned before animating");
            return default;
        }
        
        // Mark as corrupted and change collision layer to prevent further breach detection
        isCorrupted = true;
        if (spatialCollider != null)
        {
            // Change to Environment layer so breach no longer detects this vegetation
            spatialCollider.SetLayer(CollisionLayers.Environment);
            spatialCollider.ForceCollisionUpdate();
        }
        
        return AnimateMaterialProperty(materialPropertyName, startValue, endValue, animationDuration, animationEase);
    }
    
    /// <summary>
    /// Gets the current value of a material property from the first active material instance
    /// </summary>
    /// <param name="propertyName">Property name to check</param>
    /// <returns>Current property value or default if not found</returns>
    private float GetCurrentMaterialValue(string propertyName)
    {
        if (childMaterialInstances == null || activeIndices.Count == 0 || string.IsNullOrEmpty(propertyName))
            return 0f;
            
        // Get value from first active material instance
        int firstActiveIndex = activeIndices[0];
        if (firstActiveIndex < childMaterialInstances.Length && 
            childMaterialInstances[firstActiveIndex] != null &&
            childMaterialInstances[firstActiveIndex].HasProperty(propertyName))
        {
            return childMaterialInstances[firstActiveIndex].GetFloat(propertyName);
        }
        
        return 0f;
    }
    
    #endregion
    
    #if UNITY_EDITOR
    [Title("Debug & Testing")]
    [ShowInInspector, ReadOnly]
    [Tooltip("Whether this vegetation has been corrupted by breach (prevents redundant detection)")]
    private bool IsCorrupted => isCorrupted;
    
    [Button("Validate Child Setup", ButtonSizes.Medium)]
    private void ValidateChildSetup()
    {
        CacheChildComponents();

        Debug.Log($"MultiVegetationController Validation for {gameObject.name}:");
        Debug.Log($"- Child objects assigned: {childSpriteObjects?.Length ?? 0}");
        Debug.Log($"- Valid SpriteRenderers found: {childSpriteRenderers?.Length ?? 0}");
        Debug.Log($"- Valid YSortingControllers found: {childYSortingControllers?.Length ?? 0}");
        Debug.Log($"- Max active sprites: {maxActiveSprites}");
        Debug.Log($"- Additional sprite probability: {additionalSpriteProbability:P0}");

        if (childSpriteRenderers != null && childYSortingControllers != null)
        {
            for (int i = 0; i < childSpriteRenderers.Length; i++)
            {
                string spriteStatus = childSpriteRenderers[i] != null ? "✓ SpriteRenderer" : "✗ Missing SpriteRenderer";
                string ySortStatus = childYSortingControllers[i] != null ? "✓ YSortingController" : "✗ Missing YSortingController";
                Debug.Log($"  Child {i}: {spriteStatus}, {ySortStatus}");
            }
        }
    }
    
    [Button("Test Random Sprites", ButtonSizes.Medium)]
    private void TestRandomSprites()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test in play mode!");
            return;
        }
        
        // Create some dummy sprites for testing
        Sprite[] testSprites = new Sprite[3];
        // In a real scenario, these would come from BiomeData
        
        SetRandomSprites(testSprites, Color.green, Random.value > 0.5f);
        Debug.Log($"Test: Activated {GetActiveCount()} out of {GetTotalChildCount()} child sprites");
    }
    
    [Button("Test Material Animation", ButtonSizes.Medium)]
    private void TestMaterialAnimation()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Material animation can only be tested in play mode!");
            return;
        }
        
        if (string.IsNullOrEmpty(materialPropertyName))
        {
            Debug.LogWarning("Material property name is empty!");
            return;
        }
        
        if (childMaterialInstances == null || activeIndices.Count == 0)
        {
            Debug.LogWarning("No material instances or active sprites found!");
            return;
        }
        
        Debug.Log($"Testing material animation: {materialPropertyName} from {startValue} to {endValue} over {animationDuration}s with {animationEase}");
        AnimateMaterialProperty(materialPropertyName, startValue, endValue, animationDuration, animationEase);
    }
    
    [Button("Set Start Value", ButtonSizes.Small)]
    private void SetStartValue()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only set values in play mode!");
            return;
        }
        
        SetMaterialProperty(materialPropertyName, startValue);
        Debug.Log($"Set {materialPropertyName} to start value: {startValue}");
    }
    
    [Button("Set End Value", ButtonSizes.Small)]
    private void SetEndValue()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only set values in play mode!");
            return;
        }
        
        SetMaterialProperty(materialPropertyName, endValue);
        Debug.Log($"Set {materialPropertyName} to end value: {endValue}");
    }
    
    [Button("Test Fade Animation", ButtonSizes.Medium)]
    private void TestFadeAnimation()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Fade animation can only be tested in play mode!");
            return;
        }
        
        Debug.Log($"Testing fade animation to alpha: {endValue} over {animationDuration}s");
        FadeToAlpha(endValue, animationDuration, animationEase);
    }
    
    [Button("Test Dissolve Animation", ButtonSizes.Medium)]
    private void TestDissolveAnimation()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Dissolve animation can only be tested in play mode!");
            return;
        }
        
        Debug.Log($"Testing dissolve animation to progress: {endValue} over {animationDuration}s");
        DissolveToProgress(endValue, animationDuration, animationEase);
    }
    
    [Button("Reset Material Properties", ButtonSizes.Medium)]
    private void ResetMaterialProperties()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only reset in play mode!");
            return;
        }
        
        if (childMaterialInstances != null && originalMaterial != null)
        {
            for (int i = 0; i < childMaterialInstances.Length; i++)
            {
                if (childMaterialInstances[i] != null)
                {
                    childMaterialInstances[i].CopyPropertiesFromMaterial(originalMaterial);
                }
            }
            Debug.Log("Reset material properties to original values on all instances");
        }
    }
    
    [Button("Test Corruption System", ButtonSizes.Medium)]
    private void TestCorruptionSystem()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Corruption test can only be run in play mode!");
            return;
        }
        
        Debug.Log($"Testing corruption system - Current state: {(isCorrupted ? "CORRUPTED" : "CLEAN")}");
        
        if (!isCorrupted)
        {
            Debug.Log("Triggering corruption animation...");
            TriggerMaterialAnimation();
            Debug.Log($"After corruption - State: {(isCorrupted ? "CORRUPTED" : "CLEAN")}, Layer: {(spatialCollider != null ? spatialCollider.Layer.ToString() : "NULL")}");
        }
        else
        {
            Debug.Log("Already corrupted - animation will not trigger again (performance optimized)");
        }
    }
    
    [Button("Reset Corruption State", ButtonSizes.Medium)]
    private void ResetCorruptionState()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only reset in play mode!");
            return;
        }
        
        isCorrupted = false;
        if (spatialCollider != null)
        {
            spatialCollider.SetLayer(CollisionLayers.Vegetation);
            spatialCollider.ForceCollisionUpdate();
        }
        Debug.Log("Reset corruption state - vegetation can now be corrupted again");
    }
    #endif
    
    void OnDestroy()
    {
        CleanupMaterialInstances();
    }
}

using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;

/// <summary>
/// Stationary turret that can operate in two modes:
/// 1. Target Mode: Attacks nearby enemies within detection range
/// 2. Random Mode: Fires in random directions for ~10 seconds before despawning
/// </summary>
[RequireComponent(typeof(SpatialCollider))]
public class StationaryTurret : MonoBehaviour, ISpawnable
{
    [Title("Turret Configuration")]
    [SerializeField] private TurretMode mode = TurretMode.Target;
    
    [Title("Detection Settings")]
    [ShowIf("mode", TurretMode.Target)]
    [SerializeField, Range(2f, 15f)] 
    private float detectionRange = 6f;
    
    [ShowIf("mode", TurretMode.Target)]
    [SerializeField] private TurretTargetType targetType = TurretTargetType.Enemies;
    
    [Title("Attack Settings")]
    [SerializeField] private GameObject projectilePrefab;
    [SerializeField] private Transform firePoint;
    [SerializeField, Range(0.2f, 2f)] private float attackCooldown = 0.8f;
    [SerializeField, Range(5f, 20f)] private float projectileSpeed = 12f;
    [SerializeField, Range(1f, 5f)] private float projectileLifetime = 3f;
    [SerializeField, Range(5f, 50f)] private float damage = 15f;
    
    [EnumToggleButtons]
    [Tooltip("Collision layer for projectiles spawned by this turret. Determines what targets can be damaged.")]
    [SerializeField] private CollisionLayers projectileLayer = CollisionLayers.EnemyProjectile;
    
    [Title("Spread Settings")]
    [SerializeField, Range(5f, 30f)] 
    [Tooltip("Random deviation angle in degrees (±15 degrees)")]
    private float spreadAngle = 15f;
    
    [Title("Random Mode Settings")]
    [ShowIf("mode", TurretMode.Random)]
    [SerializeField, Range(5f, 15f)] 
    private float randomModeDuration = 10f;
    
    [ShowIf("mode", TurretMode.Random)]
    [SerializeField, Range(0.1f, 1f)] 
    private float randomFireRate = 0.4f;
    
    [Title("Visual Settings")]
    [SerializeField] private bool rotateToTarget = true;
    [SerializeField] private float rotationSpeed = 180f;
    
    [Title("Animation Settings")]
    [SerializeField] private bool useScaleAnimation = true;
    [ShowIf("useScaleAnimation")]
    [SerializeField, Range(0.1f, 2f)] private float spawnAnimationDuration = 0.5f;
    [ShowIf("useScaleAnimation")]
    [SerializeField, Range(0.1f, 2f)] private float despawnAnimationDuration = 0.3f;
    [ShowIf("useScaleAnimation")]
    [SerializeField] private EaseType spawnEaseType = EaseType.EaseOutBack;
    [ShowIf("useScaleAnimation")]
    [SerializeField] private EaseType despawnEaseType = EaseType.EaseInQuad;
    
    [Title("Debug")]
    [SerializeField] private bool enableDebugLogs = false;
    [SerializeField] private bool showDetectionRange = true;
    
    // Cached components
    private SpatialCollider spatialCollider;
    
    // Runtime state
    private float lastAttackTime = -999f;
    private Coroutine activeCoroutine;
    private Transform currentTarget;
    private bool isActive = false;
    
    // Animation state
    private bool isTweening = false;
    private Coroutine scaleCoroutine;
    private Vector3 originalScale;
    
    // Target detection cache
    private readonly List<ICollidable> nearbyTargets = new List<ICollidable>();
    
    public enum TurretMode
    {
        Target,
        Random
    }
    
    public enum TurretTargetType
    {
        Enemies,    // Turret attacks enemies (helps player)
        Player      // Turret attacks player (trap/boss mechanic)
    }
    
    public enum EaseType
    {
        Linear,
        EaseInQuad,
        EaseOutQuad,
        EaseInOutQuad,
        EaseInCubic,
        EaseOutCubic,
        EaseInOutCubic,
        EaseInBack,
        EaseOutBack,
        EaseInOutBack,
        EaseInElastic,
        EaseOutElastic,
        EaseInOutElastic
    }
    
    #region Unity Lifecycle
    
    private void Awake()
    {
        spatialCollider = GetComponent<SpatialCollider>();
        
        // Set up spatial collider for turret detection
        spatialCollider.SetLayer(CollisionLayers.Environment);
        spatialCollider.SetTrigger(true);
        spatialCollider.radius = detectionRange;
        
        if (firePoint == null)
            firePoint = transform;
            
        // Store original scale for animations
        originalScale = transform.localScale;
    }
    
    private void Start()
    {
        if (isActive)
        {
            StartTurretOperation();
        }
    }
    
    private void OnDestroy()
    {
        StopTurretOperation();
    }
    
    #endregion
    
    #region ISpawnable Implementation
    
    public void OnSpawn()
    {
        isActive = true;
        lastAttackTime = -999f;
        currentTarget = null;
        
        if (enableDebugLogs)
            Debug.Log($"[StationaryTurret] {name}: Spawned in {mode} mode");
        
        // Start spawn animation
        if (useScaleAnimation)
        {
            StartSpawnAnimation();
        }
        else
        {
            transform.localScale = originalScale;
            StartTurretOperation();
        }
    }
    
    public void OnDespawn()
    {
        isActive = false;
        StopTurretOperation();
        
        // Stop any running scale animation
        if (scaleCoroutine != null)
        {
            StopCoroutine(scaleCoroutine);
            scaleCoroutine = null;
        }
        
        // Reset scale and tween state
        transform.localScale = originalScale;
        isTweening = false;
        
        if (enableDebugLogs)
            Debug.Log($"[StationaryTurret] {name}: Despawned");
    }
    
    #endregion
    
    #region Turret Operations
    
    private void StartTurretOperation()
    {
        if (!isActive || activeCoroutine != null) return;
        
        switch (mode)
        {
            case TurretMode.Target:
                activeCoroutine = StartCoroutine(TargetModeCoroutine());
                break;
            case TurretMode.Random:
                activeCoroutine = StartCoroutine(RandomModeCoroutine());
                break;
        }
    }
    
    private void StopTurretOperation()
    {
        if (activeCoroutine != null)
        {
            StopCoroutine(activeCoroutine);
            activeCoroutine = null;
        }
        currentTarget = null;
    }
    
    #endregion
    
    #region Target Mode
    
    private IEnumerator TargetModeCoroutine()
    {
        while (isActive)
        {
            // Update spatial collider radius if detection range changed
            if (spatialCollider.radius != detectionRange)
            {
                spatialCollider.radius = detectionRange;
                spatialCollider.ForceCollisionUpdate();
            }
            
            // Find and engage targets
            Transform target = FindNearestTarget();
            if (target != null && CanAttack())
            {
                AttackTarget(target);
            }
            
            yield return new WaitForSeconds(0.1f); // Update frequency
        }
    }
    
    private Transform FindNearestTarget()
    {
        if (CollisionManager.Instance == null) return null;
        
        // Determine target layer based on turret configuration
        CollisionLayers targetLayer = targetType == TurretTargetType.Enemies 
            ? CollisionLayers.Enemy 
            : CollisionLayers.Player;
        
        // Use the collision manager to find targets in range
        CollisionManager.Instance.GetCollidersInRadiusNonAlloc(
            transform.position, detectionRange, nearbyTargets, targetLayer);
        
        Transform nearestTarget = null;
        float nearestDistance = float.MaxValue;
        
        foreach (var collidable in nearbyTargets)
        {
            if (collidable?.GameObject == null) continue;
            
            float distance = Vector2.Distance(transform.position, collidable.Position);
            if (distance < nearestDistance)
            {
                nearestDistance = distance;
                nearestTarget = collidable.Transform;
            }
        }
        
        // Update current target for visual feedback
        currentTarget = nearestTarget;
        
        return nearestTarget;
    }
    
    #endregion
    
    #region Random Mode
    
    private IEnumerator RandomModeCoroutine()
    {
        float endTime = Time.time + randomModeDuration;
        
        if (enableDebugLogs)
            Debug.Log($"[StationaryTurret] {name}: Starting random mode for {randomModeDuration} seconds");
        
        while (isActive && Time.time < endTime)
        {
            if (CanAttack())
            {
                AttackRandomDirection();
            }
            
            yield return new WaitForSeconds(randomFireRate);
        }
        
        // Despawn after random mode duration
        if (enableDebugLogs)
            Debug.Log($"[StationaryTurret] {name}: Random mode completed, despawning");
        
        DespawnTurret();
    }
    
    private void AttackRandomDirection()
    {
        // Generate random direction
        float randomAngle = Random.Range(0f, 360f);
        Vector2 randomDirection = new Vector2(
            Mathf.Cos(randomAngle * Mathf.Deg2Rad),
            Mathf.Sin(randomAngle * Mathf.Deg2Rad)
        );
        
        FireProjectile(randomDirection);
        
        if (rotateToTarget)
        {
            RotateToDirection(randomDirection);
        }
    }
    
    #endregion
    
    #region Attack System
    
    private bool CanAttack()
    {
        return !isTweening && Time.time - lastAttackTime >= attackCooldown;
    }
    
    private void AttackTarget(Transform target)
    {
        if (target == null || !CanAttack()) return;
        
        // Calculate direction to target
        Vector2 directionToTarget = (target.position - firePoint.position).normalized;
        
        // Apply spread (±15 degrees random deviation)
        Vector2 finalDirection = ApplySpread(directionToTarget);
        
        FireProjectile(finalDirection);
        
        if (rotateToTarget)
        {
            RotateToDirection(finalDirection);
        }
        
        if (enableDebugLogs)
            Debug.Log($"[StationaryTurret] {name}: Attacking {target.name}");
    }
    
    private Vector2 ApplySpread(Vector2 baseDirection)
    {
        // Apply random spread of ±spreadAngle degrees
        float randomSpread = Random.Range(-spreadAngle, spreadAngle);
        float currentAngle = Mathf.Atan2(baseDirection.y, baseDirection.x) * Mathf.Rad2Deg;
        float finalAngle = currentAngle + randomSpread;
        
        return new Vector2(
            Mathf.Cos(finalAngle * Mathf.Deg2Rad),
            Mathf.Sin(finalAngle * Mathf.Deg2Rad)
        );
    }
    
    private void FireProjectile(Vector2 direction)
    {
        if (projectilePrefab == null || PoolManager.Instance == null) return;
        
        Vector3 spawnPosition = firePoint.position;
        float angle = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;
        
        GameObject projectileObj = PoolManager.Instance.Spawn(
            projectilePrefab, spawnPosition, Quaternion.AngleAxis(angle, Vector3.forward));
        
        if (projectileObj != null && PoolManager.Instance.GetCachedComponent<Projectile>(projectileObj, out var projectile))
        {
            // Initialize projectile with configurable collision layer
            projectile.Initialize(
                spawnPosition,
                direction,
                damage,
                projectileLayer,
                projectileSpeed,
                projectileLifetime
            );
            
            lastAttackTime = Time.time;
            
            if (enableDebugLogs)
            {
                string targetDesc = targetType == TurretTargetType.Enemies ? "enemies" : "player";
                string layerDesc = projectileLayer.ToString();
                Debug.Log($"[StationaryTurret] {name}: Fired projectile targeting {targetDesc} using layer {layerDesc} in direction {direction}");
            }
        }
    }
    
    private void RotateToDirection(Vector2 direction)
    {
        float targetAngle = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;
        float currentAngle = transform.eulerAngles.z;
        
        // Smooth rotation towards target
        float newAngle = Mathf.MoveTowardsAngle(currentAngle, targetAngle, rotationSpeed * Time.deltaTime);
        transform.rotation = Quaternion.AngleAxis(newAngle, Vector3.forward);
    }
    
    #endregion
    
    #region Utility Methods
    
    private void DespawnTurret()
    {
        // Start despawn animation if enabled
        if (useScaleAnimation && !isTweening)
        {
            StartDespawnAnimation();
        }
        else
        {
            // Immediate despawn
            PerformDespawn();
        }
    }
    
    private void PerformDespawn()
    {
        if (PoolManager.Instance != null)
        {
            PoolManager.Instance.Despawn(gameObject);
        }
        else
        {
            Debug.LogError($"[StationaryTurret] {name}: PoolManager.Instance is null during despawn! This should never happen in a pooled system.");
            // Call OnDespawn to ensure proper cleanup even if we can't return to pool
            OnDespawn();
            // DO NOT Destroy - let the object remain in scene as error evidence
        }
    }
    
    /// <summary>
    /// Set the turret mode. Call this before spawning or during runtime.
    /// </summary>
    public void SetMode(TurretMode newMode)
    {
        if (mode != newMode)
        {
            mode = newMode;
            
            if (isActive)
            {
                // Restart with new mode
                StopTurretOperation();
                StartTurretOperation();
            }
        }
    }
    
    /// <summary>
    /// Set the target type. Call this before spawning or during runtime.
    /// </summary>
    public void SetTargetType(TurretTargetType newTargetType)
    {
        targetType = newTargetType;
        
        if (enableDebugLogs)
        {
            string targetDesc = targetType == TurretTargetType.Enemies ? "enemies" : "player";
            Debug.Log($"[StationaryTurret] {name}: Target type changed to {targetDesc}");
        }
    }
    
    /// <summary>
    /// Set detection range for target mode
    /// </summary>
    public void SetDetectionRange(float range)
    {
        detectionRange = Mathf.Clamp(range, 2f, 15f);
        if (spatialCollider != null)
        {
            spatialCollider.radius = detectionRange;
            spatialCollider.ForceCollisionUpdate();
        }
    }
    
    /// <summary>
    /// Set attack parameters
    /// </summary>
    public void SetAttackParameters(float newDamage, float newCooldown, float newSpeed)
    {
        damage = newDamage;
        attackCooldown = Mathf.Max(0.1f, newCooldown);
        projectileSpeed = Mathf.Max(1f, newSpeed);
    }
    
    /// <summary>
    /// Set the projectile collision layer. Determines what targets the turret can damage.
    /// </summary>
    public void SetProjectileLayer(CollisionLayers newProjectileLayer)
    {
        projectileLayer = newProjectileLayer;
        
        if (enableDebugLogs)
        {
            Debug.Log($"[StationaryTurret] {name}: Projectile layer changed to {projectileLayer}");
        }
    }
    
    #endregion
    
    #region Scale Animation System
    
    private void StartSpawnAnimation()
    {
        if (scaleCoroutine != null)
        {
            StopCoroutine(scaleCoroutine);
        }
        
        transform.localScale = Vector3.zero;
        isTweening = true;
        scaleCoroutine = StartCoroutine(ScaleTween(Vector3.zero, originalScale, spawnAnimationDuration, spawnEaseType, OnSpawnAnimationComplete));
    }
    
    private void StartDespawnAnimation()
    {
        if (scaleCoroutine != null)
        {
            StopCoroutine(scaleCoroutine);
        }
        
        isTweening = true;
        scaleCoroutine = StartCoroutine(ScaleTween(transform.localScale, Vector3.zero, despawnAnimationDuration, despawnEaseType, OnDespawnAnimationComplete));
    }
    
    private void OnSpawnAnimationComplete()
    {
        isTweening = false;
        scaleCoroutine = null;
        StartTurretOperation();
        
        if (enableDebugLogs)
            Debug.Log($"[StationaryTurret] {name}: Spawn animation completed, starting operation");
    }
    
    private void OnDespawnAnimationComplete()
    {
        isTweening = false;
        scaleCoroutine = null;
        PerformDespawn();
        
        if (enableDebugLogs)
            Debug.Log($"[StationaryTurret] {name}: Despawn animation completed, despawning");
    }
    
    private IEnumerator ScaleTween(Vector3 startScale, Vector3 endScale, float duration, EaseType easeType, System.Action onComplete)
    {
        float elapsedTime = 0f;
        
        while (elapsedTime < duration)
        {
            float t = elapsedTime / duration;
            float easedT = ApplyEase(t, easeType);
            
            transform.localScale = Vector3.Lerp(startScale, endScale, easedT);
            
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        
        // Ensure final scale is exact
        transform.localScale = endScale;
        onComplete?.Invoke();
    }
    
    private float ApplyEase(float t, EaseType easeType)
    {
        switch (easeType)
        {
            case EaseType.Linear:
                return t;
            case EaseType.EaseInQuad:
                return t * t;
            case EaseType.EaseOutQuad:
                return t * (2f - t);
            case EaseType.EaseInOutQuad:
                return t < 0.5f ? 2f * t * t : -1f + (4f - 2f * t) * t;
            case EaseType.EaseInCubic:
                return t * t * t;
            case EaseType.EaseOutCubic:
                return (--t) * t * t + 1f;
            case EaseType.EaseInOutCubic:
                return t < 0.5f ? 4f * t * t * t : (t - 1f) * (2f * t - 2f) * (2f * t - 2f) + 1f;
            case EaseType.EaseInBack:
                const float c1 = 1.70158f;
                const float c3 = c1 + 1f;
                return c3 * t * t * t - c1 * t * t;
            case EaseType.EaseOutBack:
                const float c1_out = 1.70158f;
                const float c3_out = c1_out + 1f;
                return 1f + c3_out * Mathf.Pow(t - 1f, 3f) + c1_out * Mathf.Pow(t - 1f, 2f);
            case EaseType.EaseInOutBack:
                const float c1_inout = 1.70158f;
                const float c2 = c1_inout * 1.525f;
                return t < 0.5f
                    ? (Mathf.Pow(2f * t, 2f) * ((c2 + 1f) * 2f * t - c2)) / 2f
                    : (Mathf.Pow(2f * t - 2f, 2f) * ((c2 + 1f) * (t * 2f - 2f) + c2) + 2f) / 2f;
            case EaseType.EaseInElastic:
                const float c4 = (2f * Mathf.PI) / 3f;
                return t == 0f ? 0f : t == 1f ? 1f : -Mathf.Pow(2f, 10f * (t - 1f)) * Mathf.Sin((t - 1.1f) * c4);
            case EaseType.EaseOutElastic:
                const float c4_out = (2f * Mathf.PI) / 3f;
                return t == 0f ? 0f : t == 1f ? 1f : Mathf.Pow(2f, -10f * t) * Mathf.Sin((t - 0.1f) * c4_out) + 1f;
            case EaseType.EaseInOutElastic:
                const float c5 = (2f * Mathf.PI) / 4.5f;
                return t == 0f ? 0f : t == 1f ? 1f : t < 0.5f
                    ? -(Mathf.Pow(2f, 20f * t - 10f) * Mathf.Sin((20f * t - 11.125f) * c5)) / 2f
                    : (Mathf.Pow(2f, -20f * t + 10f) * Mathf.Sin((20f * t - 11.125f) * c5)) / 2f + 1f;
            default:
                return t;
        }
    }
    
    #endregion
    
    #region Debug Visualization
    
    private void OnDrawGizmos()
    {
        if (!showDetectionRange) return;
        
        // Draw detection range for target mode
        if (mode == TurretMode.Target)
        {
            Gizmos.color = Color.yellow;
            DrawWireCircle(transform.position, detectionRange);
        }
        
        // Draw line to current target
        if (Application.isPlaying && currentTarget != null)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawLine(firePoint.position, currentTarget.position);
        }
        
        // Draw fire point
        if (firePoint != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(firePoint.position, 0.1f);
        }
    }
    
    private void DrawWireCircle(Vector3 center, float radius)
    {
        int segments = 32;
        float angleStep = 360f / segments;
        
        for (int i = 0; i < segments; i++)
        {
            float angle1 = i * angleStep * Mathf.Deg2Rad;
            float angle2 = (i + 1) * angleStep * Mathf.Deg2Rad;
            
            Vector3 point1 = center + new Vector3(Mathf.Cos(angle1) * radius, Mathf.Sin(angle1) * radius, 0);
            Vector3 point2 = center + new Vector3(Mathf.Cos(angle2) * radius, Mathf.Sin(angle2) * radius, 0);
            
            Gizmos.DrawLine(point1, point2);
        }
    }
    
    #endregion
}
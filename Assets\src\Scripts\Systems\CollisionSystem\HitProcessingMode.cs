/// <summary>
/// Defines how collision detection should process multiple hits in a single detection query.
/// Used by CollisionDetector and Physics2D-based collision systems for performance optimization.
/// </summary>
public enum HitProcessingMode
{
    /// <summary>
    /// Process only the first collision detected.
    /// Best for: Projectiles, triggers, single-target spells
    /// Performance: Optimal (early exit after first hit)
    /// </summary>
    FirstHitOnly,
    
    /// <summary>
    /// Process all collisions detected in the query.
    /// Best for: Area effects, explosions, multi-target spells
    /// Performance: Moderate (processes all detected collisions)
    /// </summary>
    AllHits,
    
    /// <summary>
    /// Process up to a maximum number of collisions.
    /// Best for: Chain spells, limited multi-target effects
    /// Performance: Good (processes limited number of hits)
    /// </summary>
    MaxHits
}

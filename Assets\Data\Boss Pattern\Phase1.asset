%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d06514da37eddd740a0aed2ee18f4916, type: 3}
  m_Name: Phase1
  m_EditorClassIdentifier: Assembly-CSharp::BossPatternManager
  attackPatterns:
  - patternName: Turret
    attackStrategy:
      rid: 2739021461079982168
    weight: 1
    minDistance: 0
    maxDistance: 999
    minHealthPercent: 0
    maxHealthPercent: 100
    canRepeat: 1
    maxConsecutiveUses: 3
    castDuration: 0
  - patternName: Melee
    attackStrategy:
      rid: 2739021454713290841
    weight: 1
    minDistance: 0
    maxDistance: 0.65
    minHealthPercent: 0
    maxHealthPercent: 100
    canRepeat: 1
    maxConsecutiveUses: 3333
    castDuration: 0
  - patternName: Arcane
    attackStrategy:
      rid: 2739021454713290900
    weight: 1
    minDistance: 0.66
    maxDistance: 999
    minHealthPercent: 0
    maxHealthPercent: 100
    canRepeat: 1
    maxConsecutiveUses: 10
    castDuration: 0
  - patternName: Range
    attackStrategy:
      rid: 2739021454713290872
    weight: 1
    minDistance: 1.01
    maxDistance: 999
    minHealthPercent: 0
    maxHealthPercent: 100
    canRepeat: 1
    maxConsecutiveUses: 3
    castDuration: 0
  selectionMethod: 3
  loopSequential: 1
  adaptiveDistanceWeight: 0.4
  adaptiveHealthWeight: 0.3
  adaptiveRandomWeight: 0.3
  enableDebugLogs: 0
  references:
    version: 2
    RefIds:
    - rid: 2739021454713290841
      type: {class: MeleeAttackStrategy, ns: , asm: Assembly-CSharp}
      data:
        damage: 10
        attackRange: 0.65
        attackCooldown: 0.75
        projectileLayer: 8
        animationName: attack
        requiresAnimation: 1
        enableDebugLogs: 0
    - rid: 2739021454713290872
      type: {class: RangedAttackStrategy, ns: , asm: Assembly-CSharp}
      data:
        attackType: 1
        damage: 15
        attackRange: 8
        attackCooldown: 0.5
        projectileLayer: 8
        projectileSpeed: 10
        projectilePrefab: {fileID: 2340940985034927489, guid: 9e2dccaa47cf6a444946890c4f97562d, type: 3}
        instantSpellPrefab: {fileID: 2340940985034927489, guid: 5ba2af05a908d6f4b8a4ba375d31dd00, type: 3}
        multiSpellSpreadRadius: 2
        spawnPoint: {fileID: 0}
        animationName: attack
        requiresAnimation: 0
        enableDebugLogs: 0
    - rid: 2739021454713290900
      type: {class: BurstRangedAttackStrategy, ns: , asm: Assembly-CSharp}
      data:
        totalProjectiles: 6
        directProjectiles: 3
        projectileDelay: 0.1
        minSpreadAngle: 5
        maxSpreadAngle: 15
        damage: 15
        attackRange: 8
        attackCooldown: 3
        projectileLayer: 8
        projectileSpeed: 10
        projectilePrefab: {fileID: 2340940985034927489, guid: 9e2dccaa47cf6a444946890c4f97562d, type: 3}
        spawnPoint: {fileID: 0}
        animationName: burst_attack
        requiresAnimation: 1
        enableDebugLogs: 0
    - rid: 2739021461079982168
      type: {class: SummonMinionsStrategy, ns: , asm: Assembly-CSharp}
      data:
        damage: 0
        attackRange: 20
        attackCooldown: 12
        projectileLayer: 8
        minionPrefabs:
        - {fileID: 8842187596116329330, guid: f7bdea77742016d44b64107df506b11a, type: 3}
        minionsToSummon: 3
        summonRadius: 4
        delayBetweenSummons: 0.15
        animationName: summon
        requiresAnimation: 0
        summonEffectPrefab: {fileID: 0}
        enableDebugLogs: 0

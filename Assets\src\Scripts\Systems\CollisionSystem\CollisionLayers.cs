[System.Flags]
public enum CollisionLayers
{
        None = 0,
        Player = 1 << 0,
        Enemy = 1 << 1,
        PlayerProjectile = 1 << 2,
        EnemyProjectile = 1 << 3,
        Wall = 1 << 4,
        Trigger = 1 << 5,
        Pickup = 1 << 6,
        Environment = 1 << 7,
        Interactable = 1 << 8,
        Vegetation = 1 << 9,
        Breach = 1 << 10,
        Shop = 1 << 11,
        
        // Common combinations
        AllProjectiles = PlayerProjectile | EnemyProjectile,
        AllCharacters = Player | Enemy,
        AllSolid = Wall | Environment,
        All = ~0
    }
    
    public static class CollisionLayerMatrix
    {
        // Define what layers can collide with each other - bidirectional check
        public static bool CanLayersCollide(CollisionLayers layerA, CollisionLayers layerB)
        {
            return CanLayersCollideOneWay(layerA, layerB) || CanLayersCollideOneWay(layerB, layerA);
        }
        
        // One-way collision check - maintains existing logic
        private static bool CanLayersCollideOneWay(CollisionLayers layerA, CollisionLayers layerB)
        {
            // Player collides with: Enemy, EnemyProjectile, Wall, Trigger, Pickup, Environment, Interactable, Breach, Shop
            if (layerA.HasFlag(CollisionLayers.Player))
            {
                return layerB.HasFlag(CollisionLayers.Enemy) ||
                       layerB.HasFlag(CollisionLayers.EnemyProjectile) ||
                       layerB.HasFlag(CollisionLayers.Wall) ||
                       layerB.HasFlag(CollisionLayers.Trigger) ||
                       layerB.HasFlag(CollisionLayers.Pickup) ||
                       layerB.HasFlag(CollisionLayers.Environment) ||
                       layerB.HasFlag(CollisionLayers.Interactable) ||
                       layerB.HasFlag(CollisionLayers.Breach) ||
                       layerB.HasFlag(CollisionLayers.Shop);
            }
            
            // Enemy collides with: Player, PlayerProjectile, Wall, Environment
            if (layerA.HasFlag(CollisionLayers.Enemy))
            {
                return layerB.HasFlag(CollisionLayers.Player) ||
                       layerB.HasFlag(CollisionLayers.PlayerProjectile) ||
                       layerB.HasFlag(CollisionLayers.Wall) ||
                       layerB.HasFlag(CollisionLayers.Environment);
            }
            
            // PlayerProjectile collides with: Enemy, Wall, Environment
            if (layerA.HasFlag(CollisionLayers.PlayerProjectile))
            {
                return layerB.HasFlag(CollisionLayers.Enemy) ||
                       layerB.HasFlag(CollisionLayers.Wall) ||
                       layerB.HasFlag(CollisionLayers.Environment);
            }
            
            // EnemyProjectile collides with: Player, Wall, Environment
            if (layerA.HasFlag(CollisionLayers.EnemyProjectile))
            {
                return layerB.HasFlag(CollisionLayers.Player) ||
                       layerB.HasFlag(CollisionLayers.Wall) ||
                       layerB.HasFlag(CollisionLayers.Environment);
            }
            
            // Walls and Environment don't actively collide with anything (they're passive)
            if (layerA.HasFlag(CollisionLayers.Wall) || layerA.HasFlag(CollisionLayers.Environment))
            {
                return false;
            }
            
            // Triggers collide with Player and Enemy
            if (layerA.HasFlag(CollisionLayers.Trigger))
            {
                return layerB.HasFlag(CollisionLayers.Player) ||
                       layerB.HasFlag(CollisionLayers.Enemy);
            }
            
            // Pickups collide with Player only
            if (layerA.HasFlag(CollisionLayers.Pickup))
            {
                return layerB.HasFlag(CollisionLayers.Player);
            }
            
            // Interactables collide with Player only
            if (layerA.HasFlag(CollisionLayers.Interactable))
            {
                return layerB.HasFlag(CollisionLayers.Player);
            }
            
            // Breach collides with Vegetation and Player (one-way detection)
            if (layerA.HasFlag(CollisionLayers.Breach))
            {
                return layerB.HasFlag(CollisionLayers.Vegetation) ||
                       layerB.HasFlag(CollisionLayers.Player);
            }
            
            // Vegetation doesn't actively collide with anything (passive)
            if (layerA.HasFlag(CollisionLayers.Vegetation))
            {
                return false;
            }
            
            // Shop collides with Player only
            if (layerA.HasFlag(CollisionLayers.Shop))
            {
                return layerB.HasFlag(CollisionLayers.Player);
            }
            
            return false;
        }
    }
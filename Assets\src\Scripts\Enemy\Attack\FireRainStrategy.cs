using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;

/// <summary>
/// Boss attack strategy that creates a fire rain area-of-effect attack.
/// Spawns multiple fire projectiles in a circular pattern around the target area.
/// </summary>
[System.Serializable]
public class FireRainStrategy : IAttackStrategy
{
    [SerializeField] private float damage = 25f;
    [SerializeField] private float attackRange = 12.0f;
    [SerializeField] private float attackCooldown = 5.0f;
    [SerializeField] private CollisionLayers projectileLayer = CollisionLayers.EnemyProjectile;
    [SerializeField] private GameObject fireProjectilePrefab;
    [SerializeField] private int projectileCount = 8;
    [SerializeField] private float rainRadius = 3.0f;
    [SerializeField] private float projectileSpeed = 8f;
    [SerializeField] private float delayBetweenProjectiles = 0.1f;
    [SerializeField] private string animationName = "fire_rain";
    [SerializeField] private bool requiresAnimation = true;
#if UNITY_EDITOR
    [SerializeField, Toolt<PERSON>("Enable debug logs for FireRainStrategy (Editor only).")]
    private bool enableDebugLogs = false;
#endif

    // Modifier system
    private AttackModifierCollection modifiers = new AttackModifierCollection();
    
    // Cooldown management
    private float lastAttackTime = -999f;
    
    // Animation event handling
    private Transform currentAttacker;
    private Transform currentTarget;
    private System.Action attackCompletionCallback;
    private bool attackExecuted = false;
    
    // Cached components (set via SetupComponents)
    private SpriteAnimator cachedSpriteAnimator;
    private EnemyAnimationController cachedAnimationController;
    private PathfindingMovement cachedPathfindingMovement;

    // IAttackStrategy Implementation
    public float Damage 
    { 
        get => damage; 
        set => damage = value; 
    }
    
    public CollisionLayers ProjectileLayer
    {
        get => projectileLayer;
        set => projectileLayer = value;
    }
    
    public float AttackRange => modifiers.GetModifiedRange(attackRange);
    public float AttackCooldown => modifiers.GetModifiedCooldown(attackCooldown);
    
    /// <summary>
    /// Attack duration for fire rain effect.
    /// Includes animation time and area effect duration.
    /// </summary>
    public float AttackDuration => requiresAnimation ? 2.5f : 1.0f; // Fire rain takes time to cast
    
    public string AttackAnimationName => animationName;
    public bool RequiresAnimation => requiresAnimation;
    public AttackModifierCollection Modifiers => modifiers;
    
    // Cooldown properties (using modified cooldown)
    public bool IsOnCooldown => Time.time - lastAttackTime < AttackCooldown;
    public float CooldownRemaining => Mathf.Max(0f, AttackCooldown - (Time.time - lastAttackTime));

    public bool CanAttack(Transform target, float distanceToTarget)
    {
        if (target == null) return false;
        if (distanceToTarget > AttackRange) return false;
        if (IsOnCooldown) return false;
        
        // Check if target is the player using PlayerManager
        return target.gameObject == PlayerManager.PlayerGameObject;
    }

    public void ExecuteAttackWithAnimation(Transform attacker, Transform target, System.Action onComplete = null)
    {
        if (!CanAttack(target, Vector3.Distance(attacker.position, target.position)))
        {
#if UNITY_EDITOR
            if (enableDebugLogs)
                UnityEngine.Debug.Log($"[FireRain] {attacker.name}: Cannot attack - CanAttack returned false");
#endif
            onComplete?.Invoke();
            return;
        }

#if UNITY_EDITOR
        if (enableDebugLogs)
            UnityEngine.Debug.Log($"[FireRain] {attacker.name}: Starting fire rain attack execution");
#endif
        
        // Start cooldown immediately
        StartCooldown();
        
        if (RequiresAnimation)
        {
            // Handle animation-based attack with frame events
            if (cachedSpriteAnimator != null && cachedSpriteAnimator.HasAnimation(animationName))
            {
                // Store target and completion callback for frame event handling
                currentAttacker = attacker;
                currentTarget = target;
                attackCompletionCallback = onComplete;
                attackExecuted = false;
                
                // Clean up any existing event subscriptions
                cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
                
                // Apply attack speed modifier to animation
                float attackSpeedBonus = modifiers.GetAttackSpeedBonus();
                float animationSpeedMultiplier = 1f + attackSpeedBonus;
                cachedSpriteAnimator.SetTemporarySpeedMultiplier(animationSpeedMultiplier);
                
                // Disable EnemyAnimationController during attack
                if (cachedAnimationController != null)
                {
                    cachedAnimationController.enabled = false;
                }
                
                // Subscribe to animation events
                cachedSpriteAnimator.OnAnimationEvent += OnAnimationFrameEvent;
                cachedSpriteAnimator.OnAnimationCompleted += OnAnimationCompleted;
                
                // Start attack animation
                cachedSpriteAnimator.PlayForced(animationName);
                
                // Timer-based fallback for animation completion
                if (cachedSpriteAnimator != null)
                {
                    cachedSpriteAnimator.StartCoroutine(AnimationCompletionTimer(2.0f));
                }
            }
            else
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    UnityEngine.Debug.Log($"[FireRain] {attacker.name}: No animation found, executing immediate attack");
#endif
                
                // No animation available, execute immediately
                PerformFireRain(attacker, target);
                onComplete?.Invoke();
            }
        }
        else
        {
            // No animation required, execute immediately
            PerformFireRain(attacker, target);
            onComplete?.Invoke();
        }
    }

    public void ExecuteAttack(Transform attacker, Transform target)
    {
        ExecuteAttackWithAnimation(attacker, target, null);
    }

    public void OnSpawnFromPool()
    {
        lastAttackTime = -999f;
        modifiers.ClearModifiers();
    }

    public void OnReturnToPool()
    {
        // No special cleanup needed
    }

    public void StartCooldown()
    {
        lastAttackTime = Time.time;
    }

    public void UpdateModifiers(List<AttackModifier> externalModifiers)
    {
        // Clear existing external modifiers
        modifiers.ClearModifiers("chunk");
        modifiers.ClearModifiers("distance_scaling");
        modifiers.ClearModifiers("random");
        modifiers.ClearModifiers("elite_zone");
        
        // Add new external modifiers
        modifiers.AddModifiers(externalModifiers);
    }

    public void SetupComponents(SpriteAnimator spriteAnimator, EnemyAnimationController animationController)
    {
        cachedSpriteAnimator = spriteAnimator;
        cachedAnimationController = animationController;
        
        // Cache PathfindingMovement component from the same GameObject
        if (spriteAnimator != null)
        {
            cachedPathfindingMovement = spriteAnimator.GetComponent<PathfindingMovement>();
        }
    }

    public void CancelAttack()
    {
        if (currentAttacker == null || attackCompletionCallback == null)
        {
            return;
        }

#if UNITY_EDITOR
        if (enableDebugLogs)
            UnityEngine.Debug.Log($"[FireRain] {currentAttacker.name}: Cancelling fire rain attack");
#endif
        
        // Stop animation and restore controller
        if (cachedSpriteAnimator != null)
        {
            cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
            cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
            cachedSpriteAnimator.StopAllCoroutines();
            cachedSpriteAnimator.ClearTemporarySpeedMultiplier();
            cachedSpriteAnimator.Stop();
        }
        
        // Re-enable EnemyAnimationController
        if (cachedAnimationController != null)
        {
            cachedAnimationController.enabled = true;
            
            if (currentAttacker != null && cachedPathfindingMovement != null)
            {
                cachedPathfindingMovement.RestoreStateAnimation();
            }
        }
        
        CleanupAttackState();
    }

    private void PerformFireRain(Transform attacker, Transform target)
    {
        if (fireProjectilePrefab == null)
        {
#if UNITY_EDITOR
            if (enableDebugLogs)
                UnityEngine.Debug.LogWarning($"[FireRain] {attacker.name}: No fire projectile prefab assigned!");
#endif
            return;
        }

        // Calculate number of projectiles with modifiers
        int modifiedProjectileCount = modifiers.GetProjectileCount() > 1 ? modifiers.GetProjectileCount() : projectileCount;
        
        // Calculate damage per projectile
        CriticalHitResult damageResult = modifiers.CalculateDamage(damage);
        
#if UNITY_EDITOR
        if (enableDebugLogs)
        {
            UnityEngine.Debug.Log($"[FireRain] {attacker.name}: Spawning {modifiedProjectileCount} fire projectiles for {damageResult.finalDamage} damage each");
        }
#endif

        // Start the fire rain coroutine
        if (cachedSpriteAnimator != null)
        {
            cachedSpriteAnimator.StartCoroutine(SpawnFireRainCoroutine(attacker, target.position, modifiedProjectileCount, damageResult));
        }
    }

    private IEnumerator SpawnFireRainCoroutine(Transform attacker, Vector3 targetPosition, int projectileCount, CriticalHitResult damageResult)
    {
        Vector3 spawnPosition = attacker.position;
        
        for (int i = 0; i < projectileCount; i++)
        {
            // Calculate spawn position in a circle around the target
            float angle = (360f / projectileCount) * i * Mathf.Deg2Rad;
            Vector3 offset = new Vector3(Mathf.Cos(angle), Mathf.Sin(angle), 0f) * rainRadius;
            Vector3 projectileSpawnPos = targetPosition + offset;
            
            // Spawn projectile from PoolManager
            if (PoolManager.Instance != null)
            {
                GameObject projectile = PoolManager.Instance.Spawn(fireProjectilePrefab, projectileSpawnPos, Quaternion.identity);
                
                if (projectile != null)
                {
                    // Set up projectile to move toward target
                    var projectileScript = projectile.GetComponent<Projectile>();
                    if (projectileScript != null)
                    {
                        Vector2 direction = (targetPosition - projectileSpawnPos).normalized;
                        projectileScript.Initialize(projectileSpawnPos, direction, damageResult.finalDamage, CollisionLayers.EnemyProjectile, projectileSpeed, 5f);
                    }
                }
            }
            
            // Wait before spawning next projectile
            if (i < projectileCount - 1)
            {
                yield return new WaitForSeconds(delayBetweenProjectiles);
            }
        }
    }

    private void OnAnimationFrameEvent(string eventName, string parameter)
    {
#if UNITY_EDITOR
        if (enableDebugLogs)
            UnityEngine.Debug.Log($"[FireRain] {currentAttacker?.name}: Animation event '{eventName}' received");
#endif
        
        // Check for fire rain events (case-insensitive)
        string lowerEventName = eventName.ToLowerInvariant();
        if ((lowerEventName == "fire" || lowerEventName == "rain" || lowerEventName == "cast") 
            && !attackExecuted && currentAttacker != null && currentTarget != null)
        {
#if UNITY_EDITOR
            if (enableDebugLogs)
                UnityEngine.Debug.Log($"[FireRain] {currentAttacker.name}: Executing fire rain from animation event '{eventName}'");
#endif
            
            PerformFireRain(currentAttacker, currentTarget);
            attackExecuted = true;
        }
    }

    private void OnAnimationCompleted(string animationName)
    {
#if UNITY_EDITOR
        if (enableDebugLogs)
            UnityEngine.Debug.Log($"[FireRain] OnAnimationCompleted: '{animationName}', expected: '{this.animationName}'");
#endif
        
        // Only process if this is our attack animation AND we have an active attack in progress
        if (animationName == this.animationName && currentAttacker != null && attackCompletionCallback != null)
        {
            // Clean up animation subscriptions
            if (cachedSpriteAnimator != null)
            {
                cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
                cachedSpriteAnimator.ClearTemporarySpeedMultiplier();
            }
            
            // Re-enable EnemyAnimationController
            if (cachedAnimationController != null)
            {
                cachedAnimationController.enabled = true;
            }
            
            // If no frame event executed the attack, do it now (fallback)
            if (!attackExecuted && currentAttacker != null && currentTarget != null)
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    UnityEngine.Debug.Log($"[FireRain] {currentAttacker.name}: Animation completed without frame event - using fallback");
#endif
                PerformFireRain(currentAttacker, currentTarget);
            }
            
            // Notify completion and cleanup
            attackCompletionCallback?.Invoke();
            CleanupAttackState();
        }
    }

    private void CleanupAttackState()
    {
        if (cachedSpriteAnimator != null)
        {
            cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
            cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
        }
        
        currentAttacker = null;
        currentTarget = null;
        attackCompletionCallback = null;
        attackExecuted = false;
    }

    private IEnumerator AnimationCompletionTimer(float duration)
    {
        yield return new WaitForSeconds(duration);
        
        // Only complete if we haven't been cleaned up already
        if (currentAttacker != null && attackCompletionCallback != null)
        {
#if UNITY_EDITOR
            if (enableDebugLogs)
                UnityEngine.Debug.Log($"[FireRain] {currentAttacker.name}: Timer-based animation completion triggered");
#endif
            
            // Clean up subscriptions
            if (cachedSpriteAnimator != null)
            {
                cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
                cachedSpriteAnimator.ClearTemporarySpeedMultiplier();
            }
            
            // Re-enable EnemyAnimationController
            if (cachedAnimationController != null)
            {
                cachedAnimationController.enabled = true;
            }
            
            // Fallback attack execution
            if (!attackExecuted && currentAttacker != null && currentTarget != null)
            {
                PerformFireRain(currentAttacker, currentTarget);
            }
            
            // Notify completion and cleanup
            attackCompletionCallback?.Invoke();
            CleanupAttackState();
        }
    }
}
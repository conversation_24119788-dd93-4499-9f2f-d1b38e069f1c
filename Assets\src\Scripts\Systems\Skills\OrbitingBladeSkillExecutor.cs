using UnityEngine;

/// <summary>
/// Strategy implementation for orbiting blade skills (Blade Vortex style).
/// Handles spawning of blades that orbit around the player, dealing continuous melee damage.
/// Supports blade stacking and area damage support gems.
/// Preserves autonomous targeting behavior and maintains zero-GC performance.
/// </summary>
public class OrbitingBladeSkillExecutor : ISkillExecutor
{
    public bool CanExecute(SkillType skillType) => skillType == SkillType.OrbitingBlade;

    public void Execute(SkillExecutor skillExecutor, int slotIndex, GemSocketController controller,
                       SkillGemData skillData, Vector3 targetPosition, bool isAutonomous)
    {
        // Pre-cache all expensive calculations to eliminate repeated allocations
        skillExecutor.CacheSkillExecutionValues(controller, slotIndex);

        // For orbiting blades, we spawn around the player, not at target position
        Vector3 spawnCenter = skillExecutor.transform.position;
        
        // Get blade count from skill data or support gems
        int bladeCount = GetBladeCount(skillData, controller);
        
        // Access execution cache for cached values
        var executionCache = skillExecutor.GetExecutionCache(slotIndex);
        
        // Calculate angle distribution for even spacing
        float angleStep = 360f / bladeCount;
        float startAngle = Random.Range(0f, 360f); // Random start to avoid predictable patterns
        
        // Spawn blades at evenly distributed angles
        for (int i = 0; i < bladeCount; i++)
        {
            float currentAngle = startAngle + (angleStep * i);
            
            GameObject bladeObj = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnCenter, Quaternion.identity);
            if (bladeObj == null) continue;
            
            // Configure orbiting blade using cached values
            if (PoolManager.Instance.GetCachedComponent<OrbitingBlade>(bladeObj, out var orbitingBlade))
            {
                // Validate cached damage to prevent 0 damage issue
                float finalDamage = executionCache.playerModifiedDamage;
                if (finalDamage <= 0f)
                {
                    // Recalculate if cache is invalid
                    skillExecutor.CacheSkillExecutionValues(controller, slotIndex);
                    executionCache = skillExecutor.GetExecutionCache(slotIndex);
                    finalDamage = executionCache.playerModifiedDamage;
                    
                    if (finalDamage <= 0f)
                    {
                        // Fallback to base damage if still invalid
                        finalDamage = skillData.baseDamage;
                    }
                }

                // Use cached values instead of expensive method calls
                orbitingBlade.damage = finalDamage;
                orbitingBlade.duration = skillData.duration;
                orbitingBlade.critChance = executionCache.finalCritChance;
                orbitingBlade.critMultiplier = executionCache.finalCritMultiplier;
                orbitingBlade.damageType = executionCache.damageBreakdown.GetPredominantType();
                orbitingBlade.ailmentChance = skillData.ailmentChance;
                orbitingBlade.damageBreakdown = executionCache.damageBreakdown;

                // Pass gem data for status effect configuration
                orbitingBlade.skillGemData = skillData;
                orbitingBlade.supportGems = skillExecutor.GetCachedSupportGems(controller, slotIndex);

                // Configure orbiting-specific properties from skill data
                orbitingBlade.orbitRadius = skillData.orbitRadius;
                orbitingBlade.rotationSpeed = skillData.rotationSpeed;

                // Apply support gem effects using cached values
                // Note: Pierce, Chain, Fork don't make sense for orbiting blades
                if (executionCache.hasAreaDamage)
                {
                    orbitingBlade.SetAreaDamage(true, executionCache.areaRadius);
                }

                // Initialize blade with starting angle and damage
                CollisionLayers layer = skillData.projectileLayer; // Use gem's collision layer
                orbitingBlade.Initialize(spawnCenter, currentAngle, finalDamage, layer, 
                                       skillData.duration, skillData.orbitRadius, skillData.rotationSpeed);
                
                // Double-check damage after initialization
                if (orbitingBlade.damage <= 0f)
                {
                    Debug.LogError($"[OrbitingBladeSkillExecutor] Blade damage is 0 after initialization! Setting to fallback: {skillData.baseDamage}");
                    orbitingBlade.damage = skillData.baseDamage;
                }
            }
            else
            {
                Debug.LogWarning($"[OrbitingBladeSkillExecutor] OrbitingBlade component not found on {skillData.skillPrefab.name}");
                // Despawn the incorrectly configured object
                PoolManager.Instance.Despawn(bladeObj);
            }
        }
    }

    /// <summary>
    /// Determine how many blades to spawn based on skill data and support gems
    /// </summary>
    private int GetBladeCount(SkillGemData skillData, GemSocketController controller)
    {
        // Start with base blade count from skill data
        int baseCount = skillData.bladeCount;
        
        // Multiple Projectiles support gem adds additional blades
        // GetTotalProjectileCount() includes base + support gem bonuses
        int totalFromSupportGems = controller.GetTotalProjectileCount();
        
        // If support gems provide projectiles, use the higher value
        // This allows Multiple Projectiles to work with blade count
        return Mathf.Max(baseCount, totalFromSupportGems);
    }
}
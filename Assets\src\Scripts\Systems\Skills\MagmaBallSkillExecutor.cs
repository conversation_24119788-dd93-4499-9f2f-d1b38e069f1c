using UnityEngine;

/// <summary>
/// Strategy implementation for magma ball skills.
/// Spawns multiple magma balls around the caster that seek out and damage enemies with burn effects.
/// Maintains zero-GC performance and supports the autonomous support gem system.
/// </summary>
public class MagmaBallSkillExecutor : ISkillExecutor
{
    public bool CanExecute(SkillType skillType) => skillType == SkillType.MagmaBall;

    public void Execute(SkillExecutor skillExecutor, int slotIndex, GemSocketController controller,
                       SkillGemData skillData, Vector3 targetPosition, bool isAutonomous)
    {
        // Pre-cache all expensive calculations to eliminate repeated allocations
        skillExecutor.CacheSkillExecutionValues(controller, slotIndex);
        
        // Get execution cache for cached values
        var executionCache = skillExecutor.GetExecutionCache(slotIndex);
        
        // Get magma ball count (using projectile count from support gems)
        int magmaBallCount = controller.GetTotalProjectileCount();
        
        // Spawn magma balls in a radius around the caster
        float spawnRadius = 2f; // Fixed 2 unit radius for magma ball spawning
        Vector3 casterPosition = skillExecutor.transform.position;
        
        for (int i = 0; i < magmaBallCount; i++)
        {
            // Calculate spawn position in circle around caster
            float angle = (360f / magmaBallCount) * i * Mathf.Deg2Rad;
            
            // Add some randomness to the angle and radius for more natural distribution
            float randomAngleOffset = Random.Range(-15f, 15f) * Mathf.Deg2Rad;
            float randomRadiusMultiplier = Random.Range(0.7f, 1.3f);
            
            angle += randomAngleOffset;
            float actualRadius = spawnRadius * randomRadiusMultiplier;
            
            Vector3 offset = new Vector3(
                Mathf.Cos(angle) * actualRadius,
                Mathf.Sin(angle) * actualRadius,
                0f
            );
            
            Vector3 spawnPosition = casterPosition + offset;
            
            // Spawn magma ball
            GameObject magmaBallObj = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, Quaternion.identity);
            if (magmaBallObj == null) continue;
            
            // Configure magma ball using cached values
            if (PoolManager.Instance.GetCachedComponent<MagmaBall>(magmaBallObj, out var magmaBall))
            {
                // Validate cached damage to prevent 0 damage issue
                float finalDamage = executionCache.playerModifiedDamage;
                if (finalDamage <= 0f)
                {
                    // Recalculate if cache is invalid
                    skillExecutor.CacheSkillExecutionValues(controller, slotIndex);
                    executionCache = skillExecutor.GetExecutionCache(slotIndex);
                    finalDamage = executionCache.playerModifiedDamage;
                    
                    if (finalDamage <= 0f)
                    {
                        // Fallback to base damage if still invalid
                        finalDamage = skillData.baseDamage;
                    }
                }

                // Set magma ball properties
                magmaBall.damage = finalDamage;
                magmaBall.critChance = executionCache.finalCritChance;
                magmaBall.critMultiplier = executionCache.finalCritMultiplier;
                magmaBall.damageType = executionCache.damageBreakdown.GetPredominantType();
                magmaBall.ailmentChance = skillData.ailmentChance;
                magmaBall.collisionLayer = skillData.projectileLayer;
                
                // Pass gem data for potential future extensions
                magmaBall.skillGemData = skillData;
                magmaBall.supportGems = skillExecutor.GetCachedSupportGems(controller, slotIndex);
                
                // Initialize magma ball with caster reference
                magmaBall.Initialize(spawnPosition, skillExecutor.transform);
                
                // Double-check damage after initialization
                if (magmaBall.damage <= 0f)
                {
                    Debug.LogError($"[MagmaBallSkillExecutor] Magma ball damage is 0 after initialization! Setting to fallback: {skillData.baseDamage}");
                    magmaBall.damage = skillData.baseDamage;
                }
            }
        }
    }
}